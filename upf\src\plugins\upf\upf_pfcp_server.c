


/*
 * Copyright (c) 2016 Cisco and/or its affiliates.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/** @file
    udp upf_pfcp server
*/

#include <math.h>

#include <vnet/ip/ip.h>
#include <vnet/session/application_interface.h>
#include <vnet/session/session.h>
#include <vnet/udp/udp.h>

#include <vppinfra/bihash_template.h>
#include <vppinfra/bihash_vec8_8.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/dpo/load_balance.h>

#include <vppinfra/bihash_template.c>
#include <vppinfra/pcap.h>

#include <ping/ping.h>
#include <vnet/ip/ip6_link.h>

#include "upf_pfcp.h"
#include "upf_pfcp_api.h"
#include "upf_pfcp_server.h"
#include <upf/upf_eth_broadcast.h>
#include <upf/upf_eth_multicast.h>
#include <upf/flowtable_eth_node.h>
#include "upf_5glan.h"
#include <upf/upf_frer.h>
#include "upf_oam.h"
/*add for dns sniffer begin by lixiao*/
#include "rte_hash.h"
#include <rte_cycles.h>
extern int rte_cycles_vmware_tsc_map;
//#include <generic/rte_cycles.h>
/*add for dns sniffer end by lixiao*/

static u8 pad_data[32] = {
    0x62, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70,
    0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69};

extern struct pfcp_ie_def upf_msg_specs[];

u32 g_run_independent_switch = SWITCH_ON;
u32 g_run_independent_timer = 1024; /* 1H - 1024H */

u32 g_urr_linked_accumulate_switch = SWITCH_ON;

typedef struct
{
  u64 session_index;
  u32 far_id;
} upf_downlink_buf_msg_t;

static char *g_sx_process_error_strings[] = {
#define _(n, s) s,
    foreach_sx_process_error
#undef _
};

vlib_node_registration_t sx_api_process_node;
static vlib_node_registration_t pfcp_timer_node;

static void upf_pfcp_response_make (sx_msg_t *resp, sx_msg_t *req, size_t len);
static void reset_response_timer (sx_msg_t *msg);

sx_server_main_t sx_server_main;

#define MAX_HDRS_LEN 100 /* Max number of bytes for headers */

extern void upf_dnn_init2(pfcp_session_establishment_request_t *req, sx_msg_t *msg);

extern void upf_redis_reget_fail_list_timer();

u8 *
format_rewrite (u8 *s, va_list *args)
{
  vnet_rewrite_header_t *rw = va_arg (*args, vnet_rewrite_header_t *);
  u32 max_data_bytes = va_arg (*args, u32);
  ASSERT (rw->data_bytes <= max_data_bytes);

  /* Format rewrite string. */
  if (rw->data_bytes > 0)
    s = format (s, "%U", format_hex, rw->data, rw->data_bytes);

  return s;
}

static void *upf_push_ethernet_to_buffer (vlib_main_t * vm, vlib_buffer_t * b,
          ip_adjacency_t *adj)
{
  ethernet_header_t *eh;

  /* make some room */
  eh = vlib_buffer_push_uninit (b, sizeof (ethernet_header_t));
  memset (eh, 0, sizeof(ethernet_header_t));

  vnet_rewrite_header_t *rw = (vnet_rewrite_header_t *)&adj->rewrite_header;

  memcpy(eh, rw->data, rw->data_bytes);

  //upf_debug("############# wuwei  eh:%U\n", format_hex, (u8 *)eh, sizeof(ethernet_header_t));

  return eh;
}

u16 upf_udp_checksum_ip4 (const void *b, u32 len, u8 * src, u8 * dst)
{
  const u16 *buf = b;
  u16 *ip_src = (u16 *) src;
  u16 *ip_dst = (u16 *) dst;
  u32 length = len;
  u32 sum = 0;

  while (len > 1)
    {
      sum += *buf++;
      if (sum & 0x80000000)
	sum = (sum & 0xFFFF) + (sum >> 16);
      len -= 2;
    }

  /* Add the padding if the packet length is odd */
  if (len & 1)
    sum += *((u8 *) buf);

  /* Add the pseudo-header */
  sum += *(ip_src++);
  sum += *ip_src;

  sum += *(ip_dst++);
  sum += *ip_dst;

  sum += clib_host_to_net_u16 (IP_PROTOCOL_UDP);
  sum += clib_host_to_net_u16 (length);

  /* Add the carries */
  while (sum >> 16)
    sum = (sum & 0xFFFF) + (sum >> 16);

  /* Return the one's complement of sum */
  return ((u16) (~sum));
}

void
upf_send_pfcp_data (sx_msg_t *msg)
{
  vlib_main_t *vm = vlib_get_main ();
  // vlib_buffer_free_list_t *fl;
  vlib_buffer_t *b0 = 0;
  vlib_buffer_t *hb = 0;
  u32 to_node_index;
  vlib_frame_t *f;
  u32 bi0 = ~0;
  u32 *to_next;
  u8 *data0;
  u32 first_buf_data_len, remaining_data_len, this_buf_data_len;
  upf_session_t *sess = NULL;

  if (NULL == msg->data)
    {
      upf_err ("the msg don't include data!");
      return;
    }

  first_buf_data_len = vlib_buffer_get_default_data_size (vm) - MAX_HDRS_LEN;
  if (vec_len (msg->data) < first_buf_data_len)
    {
      first_buf_data_len = vec_len (msg->data);
    }

  if (vlib_buffer_alloc (vm, &bi0, 1) != 1)
    {
      upf_err ("can't allocate buffer for Sx send event");
      return;
    }

  b0 = vlib_get_buffer (vm, bi0);
  hb = b0;

  VLIB_BUFFER_TRACE_TRAJECTORY_INIT (b0);

  b0->error = 0;
  b0->flags = VNET_BUFFER_F_LOCALLY_ORIGINATED;
  b0->current_data = 0;
  b0->total_length_not_including_first_buffer = 0;
  vnet_buffer (b0)->sw_if_index[VLIB_RX] = 0;
  vnet_buffer (b0)->sw_if_index[VLIB_TX] = msg->fib_index;

  data0 = vlib_buffer_make_headroom (b0, MAX_HDRS_LEN);
  clib_memcpy (data0, msg->data, first_buf_data_len);

  remaining_data_len = vec_len (msg->data) - first_buf_data_len;

  while (remaining_data_len)
    {
      this_buf_data_len =
          remaining_data_len < vlib_buffer_get_default_data_size (vm)
              ? remaining_data_len
              : vlib_buffer_get_default_data_size (vm);
      if (vlib_buffer_alloc (vm, &b0->next_buffer, 1) != 1)
        return;

      b0->flags |= VLIB_BUFFER_NEXT_PRESENT;
      /* move on to the newly acquired buffer */
      b0 = vlib_get_buffer (vm, b0->next_buffer);

      clib_memcpy (b0->data,
                   &msg->data[vec_len (msg->data) - remaining_data_len],
                   this_buf_data_len);

      b0->current_length = this_buf_data_len;
      b0->current_data = 0;
      remaining_data_len -= this_buf_data_len;
    }

  hb->flags |= VLIB_BUFFER_TOTAL_LENGTH_VALID;
  hb->current_length = first_buf_data_len;
  hb->total_length_not_including_first_buffer =
      vec_len (msg->data) - first_buf_data_len;

  vlib_buffer_push_udp (hb, msg->lcl.port, msg->rmt.port, 1);
  if (ip46_address_is_ip4 (&msg->rmt.address))
    {
      ip4_header_t *ih;
      ih = vlib_buffer_push_ip4 (vm, hb, &msg->lcl.address.ip4,
                                 &msg->rmt.address.ip4, IP_PROTOCOL_UDP, 1);
      ih->flags_and_fragment_offset = 0;
      to_node_index = ip4_lookup_node.index;
    }
  else
    {
      ip6_header_t *ih;
      ih = vlib_buffer_push_ip6 (vm, hb, &msg->lcl.address.ip6,
                                 &msg->rmt.address.ip6, IP_PROTOCOL_UDP);
      vnet_buffer (hb)->l3_hdr_offset = (u8 *)ih - hb->data;
      to_node_index = ip6_lookup_node.index;
    }

  upf_debug ("send pfcp to %p %U:%d from %U:%d, type %d", msg,
             format_ip46_address, &msg->rmt.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->rmt.port), format_ip46_address,
             &msg->lcl.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->lcl.port), msg->hdr->type);

  upf_pfcp_statistic (msg->hdr->type, msg->cause, DIRECTION_TX);

  if (msg->hdr->s_flag)
  {
      upf_dnn_pfcp_statistic (msg->dnn, msg->hdr->type, msg->cause, DIRECTION_TX);
      upf_nwi_pfcp_statistic (msg->s_nssai, msg->hdr->type, msg->cause, DIRECTION_TX);
  }

  if (g_single_trace_flag)
  {
      u32 lb_index = 0;
      u8 is_ip4 = ip46_address_is_ip4 (&msg->rmt.address);
      if (is_ip4)
      {
        lb_index = (u32)ip4_fib_forwarding_lookup (msg->fib_index, &msg->rmt.address.ip4);
      }
      else
      {
        lb_index = (u32)ip6_fib_table_fwding_lookup (msg->fib_index, &msg->rmt.address.ip6);
      }

      const dpo_id_t *dpo = load_balance_get_bucket_i (load_balance_get (lb_index), 0);
      // Add dor elb route mode need find dpo 2 times by liupeng on 2021-11-24 below
      if(dpo->dpoi_type == DPO_LOAD_BALANCE)
      {
          dpo = load_balance_get_bucket_i (load_balance_get (dpo->dpoi_index), 0);
      }
      // Add dor elb route mode need find dpo 2 times by liupeng on 2021-11-24 above
      if (dpo->dpoi_type == DPO_ADJACENCY)
      {
        ip_adjacency_t *adj = adj_get (dpo->dpoi_index);
        upf_push_ethernet_to_buffer(vm, hb, adj);

        u8 *data = vlib_buffer_get_current (hb);

        if (is_ip4)
        {
            ip4_header_t *ip0 = (ip4_header_t *)(data + sizeof(ethernet_header_t));
            ip_csum_t sum0;
            ip4_partial_header_checksum_x1 (ip0, sum0);
            ip0->checksum = ~ip_csum_fold (sum0);
            udp_header_t *udp0 = (udp_header_t *)((u8 *)ip0 + sizeof(ip4_header_t));
            udp0->checksum = upf_udp_checksum_ip4 (udp0, clib_net_to_host_u16 (udp0->length), ip0->src_address.as_u8, ip0->dst_address.as_u8);
        }
        else
        {
            //ipv6
        }

        //upf_debug ("code:\n%U%U\n", format_rewrite, &adj->rewrite_header, sizeof (adj->rewrite_data),
        //      format_hex, (u8 *)(data), hb->current_length);
        sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid));
        if (sess)
        {
          memcpy(&msg->user_id, &sess->user_id, sizeof(msg->user_id));
          msg->user_id.nai = NULL;
          if (sess->single_trace_flag != g_single_trace_flag)
          {
            upf_debug("pfcp single trace updata\n");
            upf_pfcp_single_trace_update(sess, msg, is_ip4);
          }
          //session message
          upf_pfcp_single_trace_push(sess, msg, data, hb->current_length, UPF_PKT_DIRECTION_OUT);
        }
        else
        {
          //node message
          upf_pfcp_node_single_trace_push(msg, data, hb->current_length, ip46_address_is_ip4 (&msg->rmt.address), UPF_PKT_DIRECTION_OUT);
        }
        vlib_buffer_advance(hb, sizeof(ethernet_header_t));
      }
  }

  f = vlib_get_frame_to_node (vm, to_node_index);
  to_next = vlib_frame_vector_args (f);
  to_next[0] = bi0;
  f->n_vectors = 1;

  vlib_put_frame_to_node (vm, to_node_index, f);

  vlib_node_increment_counter (vm, sx_api_process_node.index,
                               SX_PROCESS_PFCP_PACKETS_SENT, 1);
}

static int
sx_session_msg_encode (upf_session_t *sx, u8 type, struct pfcp_group *grp,
                       sx_msg_t *msg)
{
  sx_server_main_t *sxs = &sx_server_main;
  int r = 0;
  u32 length = 0;

  r = upf_pfcp_msg_length (type, grp, &length);
  if (r != 0)
    {
      return r;
    }
  length = (length + PFCP_MSG_LENGTH_ALIGN - 1) & ~(PFCP_MSG_LENGTH_ALIGN - 1);

  init_sx_msg (msg);

  msg->seq_no = clib_atomic_add_fetch (&sxs->seq_no, 1) % 0x1000000;
  msg->node = sx->assoc.node;
  msg->session_index = sx->up_seid;
  msg->data = vec_new (u8, length);

  msg->hdr->version = 1;
  msg->hdr->s_flag = 1;
  msg->hdr->type = type;

  msg->hdr->session_hdr.seid = clib_host_to_net_u64 (sx->cp_seid);
  msg->hdr->session_hdr.sequence[0] = (msg->seq_no >> 16) & 0xff;
  msg->hdr->session_hdr.sequence[1] = (msg->seq_no >> 8) & 0xff;
  msg->hdr->session_hdr.sequence[2] = msg->seq_no & 0xff;

  _vec_len (msg->data) = offsetof (pfcp_header_t, session_hdr.ies);

  r = upf_pfcp_encode_msg (type, grp, &msg->data);
  if (r != 0)
    {
      vec_free (msg->data);
      return r;
    }

  msg->hdr->length = clib_host_to_net_u16 (_vec_len (msg->data) - 4);

  msg->fib_index = sx->fib_index, msg->lcl.address = sx->up_address;
  msg->rmt.address = sx->cp_address;
  msg->lcl.port = clib_host_to_net_u16 (PFCP_UDP_DST_PORT);
  msg->rmt.port = clib_host_to_net_u16 (PFCP_UDP_DST_PORT);
  // upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d\n",
  // msg->fib_index,
  // format_ip46_address, &msg->lcl.address, IP46_TYPE_ANY,
  // clib_net_to_host_u16 (msg->lcl.port),
  // format_ip46_address, &msg->rmt.address, IP46_TYPE_ANY,
  // clib_net_to_host_u16 (msg->rmt.port));
  memcpy(&msg->user_id, &sx->user_id, sizeof(msg->user_id));
  msg->user_id.nai = NULL;

  msg->s_nssai = sx->s_nssai;
  msg->dnn = format (msg->dnn, "%s", sx->dnn);

  return 0;
}

static int
sx_node_msg_encode (upf_node_assoc_t *n, u8 type, struct pfcp_group *grp,
                    sx_msg_t *msg)
{
  sx_server_main_t *sxsm = &sx_server_main;
  upf_main_t *gtm = &g_upf_main;
  int r = 0;

  init_sx_msg (msg);

  msg->seq_no = clib_atomic_add_fetch (&sxsm->seq_no, 1) % 0x1000000;
  msg->node = n - gtm->nodes;
  msg->data = vec_new (u8, 2048);

  msg->hdr->version = 1;
  msg->hdr->s_flag = 0;
  msg->hdr->type = type;

  msg->hdr->msg_hdr.sequence[0] = (msg->seq_no >> 16) & 0xff;
  msg->hdr->msg_hdr.sequence[1] = (msg->seq_no >> 8) & 0xff;
  msg->hdr->msg_hdr.sequence[2] = msg->seq_no & 0xff;

  _vec_len (msg->data) = offsetof (pfcp_header_t, msg_hdr.ies);

  r = upf_pfcp_encode_msg (type, grp, &msg->data);
  if (r != 0)
    {
      vec_free (msg->data);
      return r;
    }

  msg->hdr->length = clib_host_to_net_u16 (_vec_len (msg->data) - 4);

  msg->fib_index = n->fib_index;
  msg->lcl.address = n->lcl_addr;
  msg->rmt.address = n->rmt_addr;
  msg->lcl.port = clib_host_to_net_u16 (PFCP_UDP_DST_PORT);
  msg->rmt.port = clib_host_to_net_u16 (PFCP_UDP_DST_PORT);
  // upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d\n",
  // msg->fib_index,
  // format_ip46_address, &msg->lcl.address, IP46_TYPE_ANY,
  // clib_net_to_host_u16 (msg->lcl.port),
  // format_ip46_address, &msg->rmt.address, IP46_TYPE_ANY,
  // clib_net_to_host_u16 (msg->rmt.port));

  return 0;
}

int
upf_pfcp_server_msg_rx (sx_msg_t *msg)
{
  sx_server_main_t *sxsm = &sx_server_main;
  int len = vec_len (msg->data);
  int error = 0;
  u8 *seq_no;

  upf_trace ("%U", upf_format_pfcp_msg_hdr, msg->hdr);

  if (msg->hdr->version != 1)
    {
      sx_msg_t resp;

      upf_err ("PFCP: msg version invalid: %d.", msg->hdr->version);

      upf_pfcp_response_make (&resp, msg, sizeof (pfcp_header_t));

      resp.hdr->version = 1;
      resp.hdr->type = PFCP_VERSION_NOT_SUPPORTED_RESPONSE;
      resp.hdr->length =
          clib_host_to_net_u16 (offsetof (pfcp_header_t, msg_hdr.ies) - 4);
      _vec_len (resp.data) = offsetof (pfcp_header_t, msg_hdr.ies);

      resp.s_nssai = msg->s_nssai;
      resp.dnn = format (resp.dnn, "%s", msg->dnn);

      upf_send_pfcp_data (&resp);
      vec_free (resp.data);

      error = -1;
      goto rtn;
    }

  if (len < (clib_net_to_host_u16 (msg->hdr->length) + 4) ||
      (!msg->hdr->s_flag && len < offsetof (pfcp_header_t, msg_hdr.ies)) ||
      (msg->hdr->s_flag && len < offsetof (pfcp_header_t, session_hdr.ies)))
    {
      upf_err ("PFCP: msg length invalid, data %d, msg %d.", len,
               clib_net_to_host_u16 (msg->hdr->length));

      error = -2;
      goto rtn;
    }

  msg->node = ~0;

  seq_no = (msg->hdr->s_flag) ? &msg->hdr->session_hdr.sequence[0]
                              : &msg->hdr->msg_hdr.sequence[0];
  msg->seq_no = (seq_no[0] << 16) | (seq_no[1] << 8) | seq_no[2];

  upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d seq %u\n", msg->fib_index,
             format_ip46_address, &msg->lcl.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->lcl.port), format_ip46_address,
             &msg->rmt.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->rmt.port), msg->seq_no);
  switch (msg->hdr->type)
    {
    case PFCP_HEARTBEAT_REQUEST:
    case PFCP_PFD_MANAGEMENT_REQUEST:
    case PFCP_ASSOCIATION_SETUP_REQUEST:
    case PFCP_ASSOCIATION_UPDATE_REQUEST:
    case PFCP_ASSOCIATION_RELEASE_REQUEST:
    case PFCP_NODE_REPORT_REQUEST:
    case PFCP_SESSION_SET_DELETION_REQUEST:
    case PFCP_SESSION_ESTABLISHMENT_REQUEST:
    case PFCP_SESSION_MODIFICATION_REQUEST:
    case PFCP_SESSION_DELETION_REQUEST:
    case PFCP_SESSION_REPORT_REQUEST:
      {
        clib_bihash_kv_40_8_t kv;

        clib_memset (&kv, 0, sizeof (kv));
        clib_memcpy (kv.key, msg->request_key, 32);

        if (clib_bihash_search_40_8 (&sxsm->response_q, &kv, &kv))
          {
            upf_pfcp_msg_handle (msg);
          }
        else
          {
            if (PREDICT_TRUE(!pool_is_free_index(sxsm->msg_pool, kv.value)))
            {
                sx_msg_t *resp = pool_elt_at_index (sxsm->msg_pool, kv.value);

                upf_info ("Response found, resend... msg idx %d SN:%d\n", kv.value,
                          resp->seq_no);
                upf_send_pfcp_data (resp);
                reset_response_timer (resp);
            }
          }
        break;
      }

    case PFCP_HEARTBEAT_RESPONSE:
    case PFCP_PFD_MANAGEMENT_RESPONSE:
    case PFCP_ASSOCIATION_UPDATE_RESPONSE:
    case PFCP_ASSOCIATION_RELEASE_RESPONSE:
    case PFCP_VERSION_NOT_SUPPORTED_RESPONSE:
    case PFCP_NODE_REPORT_RESPONSE:
    case PFCP_SESSION_SET_DELETION_RESPONSE:
    case PFCP_SESSION_ESTABLISHMENT_RESPONSE:
    case PFCP_SESSION_MODIFICATION_RESPONSE:
    case PFCP_SESSION_DELETION_RESPONSE:
    case PFCP_SESSION_REPORT_RESPONSE:
      {
        sx_msg_t *req;
        clib_bihash_kv_8_8_t kv;

        kv.key = 0;
        kv.value = ~0;

        kv.key = msg->seq_no;
        if (clib_bihash_search_inline_8_8 (&sxsm->request_q, &kv))
          {
            upf_warn ("Msg Seq No: %u request not found\n", msg->seq_no);
            break;
          }

        if (PREDICT_TRUE(!pool_is_free_index(sxsm->msg_pool, kv.value)))
        {
            req = pool_elt_at_index (sxsm->msg_pool, kv.value);
            upf_debug ("Msg Seq No: %u, request msg_pool idx %u\n", msg->seq_no, kv.value);

            clib_bihash_add_del_8_8 (&sxsm->request_q, &kv, 0 /* is_del*/);
            upf_pfcp_server_timer_stop (req->timer.handle);

            msg->node = req->node;
            sx_msg_free (sxsm, req);
        }
        upf_pfcp_msg_handle (msg);
        break;
      }
    case PFCP_ASSOCIATION_SETUP_RESPONSE:
      upf_pfcp_msg_handle (msg);
      break;

    default:
      break;
    }

rtn:

  if (vec_len (msg->dnn) > 0)
    vec_free (msg->dnn);

  if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
    {
        if (msg->data)
            free (msg->data);
        free(msg);
    }
  else
    {
        vec_free (msg->data);
        clib_mem_free (msg);
    }

  return error;
}

static sx_msg_t *
sx_session_msg_build (upf_session_t *sx, u8 type, struct pfcp_group *grp)
{
  sx_server_main_t *sxsm = &sx_server_main;
  sx_msg_t *msg;
  u32 msg_index;
  int r = 0;

  msg_index = upf_msg_pool_instance_alloc ();
  CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, NULL);
  msg = pool_elt_at_index (sxsm->msg_pool, msg_index);

  if ((r = sx_session_msg_encode (sx, type, grp, msg)) != 0)
    {
      upf_msg_pool_instance_free (msg_index);
      return NULL;
    }
  vec_add1 (sx->msgs_id, msg - sxsm->msg_pool);
  return msg;
}

static sx_msg_t *
sx_node_msg_build (upf_node_assoc_t *n, u8 type, struct pfcp_group *grp)
{
  sx_server_main_t *sxsm = &sx_server_main;
  sx_msg_t *msg;
  u32 msg_index;
  int r = 0;

  msg_index = upf_msg_pool_instance_alloc ();
  CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, NULL);
  msg = pool_elt_at_index (sxsm->msg_pool, msg_index);
  if ((r = sx_node_msg_encode (n, type, grp, msg)) != 0)
    {
      upf_msg_pool_instance_free (msg_index);
      return NULL;
    }

  return msg;
}

int
upf_pfcp_request_send (upf_session_t *sx, u8 type, struct pfcp_group *grp,
                       u32 event_type)
{
  sx_msg_t *msg;
  sx_event_t *event;
  int r = -1;

  //g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics.sess_report.req_times++;

  event = clib_mem_alloc_no_fail (sizeof (*event));
  memset (event, 0, sizeof (sx_event_t));

  msg = clib_mem_alloc_aligned_no_fail (sizeof (*msg), CLIB_CACHE_LINE_BYTES);
  memset (msg, 0, sizeof (*msg));

  if ((r = sx_session_msg_encode (sx, type, grp, msg)) != 0)
    {
      clib_mem_free (msg);
      goto out_free;
    }

  upf_debug ("sending NOTIFY event %p", msg);
  event->event_type = event_type;
  event->msg = msg;
  pfcp_thread_send_sx_event_to_thread (
      sx->thread_index - g_upf_main.first_pfcp_thread_index, event);

out_free:
  upf_pfcp_free_msg (type, grp);
  return r;
}

static void
sx_enqueue_request (sx_msg_t *msg, u32 n1, u32 t1)
{
  sx_server_main_t *sxsm = &sx_server_main;
  u32 id = msg - sxsm->msg_pool;
  clib_bihash_kv_8_8_t kv;

  clib_memset (&kv, 0, sizeof (kv));
  upf_debug ("Msg Seq No: %u, idx %u\n", msg->seq_no, id);
  msg->n1 = n1;
  msg->t1 = t1;

  kv.key = msg->seq_no;
  kv.value = id;
  clib_bihash_add_del_8_8 (&sxsm->request_q, &kv, 1 /* is_add */);

  msg->timer.base = unix_time_now ();
  msg->timer.period = t1;
  upf_pfcp_server_timer_start (PFCP_SERVER_T1, id, &msg->timer);
}

u8* upf_time_format_get()
{
    u8 *msg = 0;
    struct tm *tm;
    struct timeval tv;

    gettimeofday (&tv, NULL);
    tm = localtime (&tv.tv_sec);

    msg = format (
        msg, "%4d/%02d/%02d %02d:%02d:%02d.%06d",
        tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday, tm->tm_hour, tm->tm_min,
        tm->tm_sec, tv.tv_usec);
    return msg;
}

// function to monitor N4 link status
// added by caozhongwei 2025-07-11
static void
upf_check_n4_link_status(upf_main_t *gtm, upf_node_assoc_t *n, u8 heartbeat_received)
{
  f64 now = unix_time_now();

  // if a heartbeat is received
  if (heartbeat_received)
  {
    // Update last heartbeat reception timestamp
    n->HB_timer.last_received = now;

    // reset lost heartbeat count
    gtm->n4_persist.missed_heartbeats = 0;

    // if the link status was anomalous, recover it now
    if (gtm->n4_persist.link_status == 0)
    {
      gtm->n4_persist.link_status = 1;

      // Reset timeout timestamp
      gtm->n4_persist.timeout_timestamp = 0;

      upf_info("N4 link with CP node(%U) restored after %u missed heartbeats",
               upf_format_node_id, &n->node_id, gtm->n4_persist.missed_heartbeats);

      // clear alarms information
      uword *p = hash_get_mem(gtm->asso_alarm_index, &n->node_id.ip);
      if (p)
      {
        upf_asso_alarm_t *asso_alarm_status;
        if (PREDICT_TRUE(!pool_is_free_index(gtm->asso_alarm_status_list, p[0])))
        {
          asso_alarm_status = pool_elt_at_index(gtm->asso_alarm_status_list, p[0]);
          if (asso_alarm_status && asso_alarm_status->status == UPF_ALARM_TYPE_PRODUCE)
          {
            asso_alarm_status->status = UPF_ALARM_TYPE_RECOVER;
          }
        }
      }
    }
    else
    {
      // Normal heartbeat received in normal state
      upf_debug("N4 heartbeat received from CP node(%U)", upf_format_node_id, &n->node_id);
    }
  }
  else
  {
    // increment lost heartbeat count
    gtm->n4_persist.missed_heartbeats++;

    // Log heartbeat loss
    if (gtm->n4_persist.missed_heartbeats == 1)
    {
      upf_debug("First N4 heartbeat missed from CP node(%U)", upf_format_node_id, &n->node_id);
    }
    else if (gtm->n4_persist.missed_heartbeats < gtm->n4_persist.max_missed_heartbeats)
    {
      upf_debug("N4 heartbeat missed from CP node(%U) (%u/%u)",
                upf_format_node_id, &n->node_id,
                gtm->n4_persist.missed_heartbeats, gtm->n4_persist.max_missed_heartbeats);
    }

    // Check if we should transition to anomaly state
    if (gtm->n4_persist.missed_heartbeats >= gtm->n4_persist.max_missed_heartbeats)
    {
      // Only transition to anomaly if currently in normal state
      if (gtm->n4_persist.link_status == 1)
      {
        // mark the N4 link as anomalous
        gtm->n4_persist.link_status = 0;

        // if session persistence is enabled
        if (gtm->n4_persist.enabled)
        {
          // set session timeout duration
          gtm->n4_persist.timeout_timestamp = now + gtm->n4_persist.persist_time;
          upf_info("N4 link with CP node(%U) down after %u missed heartbeats, sessions will persist for %u seconds",
                   upf_format_node_id, &n->node_id,
                   gtm->n4_persist.missed_heartbeats, gtm->n4_persist.persist_time);
        }
        else
        {
          upf_info("N4 link with CP node(%U) down after %u missed heartbeats, session persistence disabled",
                   upf_format_node_id, &n->node_id, gtm->n4_persist.missed_heartbeats);
        }

        // trigger alarm
        uword *p = hash_get_mem(gtm->asso_alarm_index, &n->node_id.ip);
        if (p)
        {
          upf_asso_alarm_t *asso_alarm_status;
          if (PREDICT_TRUE(!pool_is_free_index(gtm->asso_alarm_status_list, p[0])))
          {
            asso_alarm_status = pool_elt_at_index(gtm->asso_alarm_status_list, p[0]);
            if (asso_alarm_status && asso_alarm_status->status == UPF_ALARM_TYPE_RECOVER)
            {
              asso_alarm_status->status = UPF_ALARM_TYPE_PRODUCE;
            }
          }
        }
      }
      else if (gtm->n4_persist.link_status == 0 && gtm->n4_persist.enabled)
      {
        // Already in anomaly state with persistence enabled - check timeout
        f64 remaining = gtm->n4_persist.timeout_timestamp - now;
        if (remaining <= 0)
        {
          upf_info("N4 session persistence timeout reached for CP node(%U)",
                   upf_format_node_id, &n->node_id);
        }
        else if (gtm->n4_persist.missed_heartbeats % 5 == 0)  // Log every 5 heartbeats
        {
          upf_debug("N4 link still down for CP node(%U), %.2f seconds remaining in persistence window",
                    upf_format_node_id, &n->node_id, remaining);
        }
      }
    }
    else
    {
      // missed_heartbeats < max_missed_heartbeats
      // If we were in anomaly state but haven't reached the threshold, we should stay in normal state
      // This handles the case where max_missed_heartbeats is increased after link was marked as anomaly
      if (gtm->n4_persist.link_status == 0)
      {
        // Reset to normal state since we're below the threshold
        gtm->n4_persist.link_status = 1;
        gtm->n4_persist.timeout_timestamp = 0;

        upf_info("N4 link with CP node(%U) restored due to threshold change (current: %u, threshold: %u)",
                 upf_format_node_id, &n->node_id,
                 gtm->n4_persist.missed_heartbeats, gtm->n4_persist.max_missed_heartbeats);

        // clear alarms information
        uword *p = hash_get_mem(gtm->asso_alarm_index, &n->node_id.ip);
        if (p)
        {
          upf_asso_alarm_t *asso_alarm_status;
          if (PREDICT_TRUE(!pool_is_free_index(gtm->asso_alarm_status_list, p[0])))
          {
            asso_alarm_status = pool_elt_at_index(gtm->asso_alarm_status_list, p[0]);
            if (asso_alarm_status && asso_alarm_status->status == UPF_ALARM_TYPE_PRODUCE)
            {
              asso_alarm_status->status = UPF_ALARM_TYPE_RECOVER;
            }
          }
        }
      }
    }
  }
}

static void
request_time_expired (u32 id)
{
  sx_server_main_t *sxsm = &sx_server_main;
  CHECK_POOL_IS_VALID_NORET(sxsm->msg_pool, id);
  sx_msg_t *msg = pool_elt_at_index (sxsm->msg_pool, id);
  upf_main_t *gtm = &g_upf_main;

  upf_debug ("Msg Seq No: %u, %p, idx %u, n1 %u\n", msg->seq_no, msg, id,
             msg->n1);

  if (--msg->n1 != 0)
    {
      // upf_debug ("resend...\n");
      msg->timer.base += msg->timer.period;
      upf_pfcp_server_timer_start (PFCP_SERVER_T1, id, &msg->timer);
      upf_send_pfcp_data (msg);
    }
  else
    {
      u8 type = 0;
      u32 node = msg->node;
      clib_bihash_kv_8_8_t kv;
      clib_memset (&kv, 0, sizeof (kv));

      if (NULL != msg->data)
        type = msg->hdr->type;
      // upf_debug ("abort...\n");
      // TODO: handle communication breakdown....

      kv.key = msg->seq_no;
      clib_bihash_add_del_8_8 (&sxsm->request_q, &kv, 0 /* is_del*/);
      sx_msg_free (sxsm, msg);

      if (type == PFCP_HEARTBEAT_REQUEST &&
          !pool_is_free_index (gtm->nodes, node) && !gtm->heart_beat_disable)
        {
          upf_node_assoc_t *n = pool_elt_at_index (gtm->nodes, msg->node);
          
          // modify the heartbeat request handler
          // added by caozhongwei 2025-07-11
          // update N4 link status - heartbeat request timeout indicates no response
          upf_check_n4_link_status(gtm, n, 0 /* heartbeat not received */);

          // Check if session persistence is enabled and link is down
          if (gtm->n4_persist.link_status == 0 && gtm->n4_persist.enabled)
          {
            f64 now = unix_time_now();
            if (now < gtm->n4_persist.timeout_timestamp)
            {
              upf_debug("N4 link down but session persist enabled, keeping association with CP node(%U) for %.2f more seconds",
                       upf_format_node_id, &n->node_id, gtm->n4_persist.timeout_timestamp - now);

              // Do not release association during persistence window
              return;  // do not release association
            }
            else
            {
              upf_info("N4 session persistence timeout reached for CP node(%U), releasing association",
                      upf_format_node_id, &n->node_id);
              // Continue to release association below
            }
          }

          // Release association logic (executed when persistence is disabled or timeout reached)
          upf_err("The CP node(%U) unreachable, release the Association.",
                 upf_format_node_id, &n->node_id);

          // association timeout alarm - keep original alarm handling
          uword *p = hash_get_mem(gtm->asso_alarm_index, &n->node_id.ip);
          u32 status = 0;
          upf_asso_alarm_t *asso_alarm_status;

          if (p)
          {
              if (PREDICT_TRUE(!pool_is_free_index(gtm->asso_alarm_status_list, p[0])))
              {
                  asso_alarm_status = pool_elt_at_index(gtm->asso_alarm_status_list, p[0]);
                  if (asso_alarm_status)
                    status = asso_alarm_status->status;
                  if (status == UPF_ALARM_TYPE_RECOVER)
                  {
                      asso_alarm_status->status = UPF_ALARM_TYPE_PRODUCE;
                  }
              }
          }
          else
          {
              pool_get(gtm->asso_alarm_status_list, asso_alarm_status);
              asso_alarm_status->remote_ip = n->node_id.ip;
              hash_set_mem_alloc(&gtm->asso_alarm_index, &asso_alarm_status->remote_ip,
                                asso_alarm_status - gtm->asso_alarm_status_list);
              status = UPF_ALARM_TYPE_RECOVER;
              asso_alarm_status->status = UPF_ALARM_TYPE_PRODUCE;
          }

          if (status == UPF_ALARM_TYPE_RECOVER)
          {
              upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail(sizeof(*upf_alarm));
              memset(upf_alarm, 0, sizeof(*upf_alarm));

              upf_alarm->data = clib_mem_alloc_aligned_no_fail(sizeof(pfcp_node_id_t), CLIB_CACHE_LINE_BYTES);
              memset(upf_alarm->data, 0, sizeof(pfcp_node_id_t));
              upf_alarm->alarm_id = UPF_ALARM_ASSOCIATION_TIMEOUT;
              upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
              upf_alarm->alarm_level = UPF_ALARM_LEVEL_SERIOUS;
              memcpy(upf_alarm->data, &n->node_id, sizeof(pfcp_node_id_t));

              for (int i = 0; i < gtm->num_pfcp_threads; i++)
              {
                  pfcp_send_sx_alarm_to_thread(i, upf_alarm);
              }
          }

          // Keep original context release control
          if (1 != gtm->context_release_disable)
              upf_pfcp_events_publish(PFCP_RPC_PUBLISH_REDIS_DEL_ASSO, n, msg);

          // Finally, release the association
          upf_pfcp_release_association(n);
        }
    }
}

void upf_gtp_echo_req (upf_peer_t *peer0, u32 peer_idx)
{
    peer0->qos_monitor_t0 = unix_time_now();

    peer0->qos_monitor_delay = ~0;
    upf_gtpu_send_echo_req (peer_idx);
}

static void
gtpu_echo_req_time_expired (u32 id)
{
  upf_main_t *gtm = &g_upf_main;
  upf_peer_t *peer0 = NULL;

  if (pool_is_free_index (gtm->peers, id))
    return;
  peer0 = pool_elt_at_index (gtm->peers, id);

  peer0->n1--;
  if (peer0->n1 > 0)
    {
      upf_debug ("resend echo req idx:%u seq:%u n1 %u remote ip:%U\n", id,
        peer0->seq, peer0->n1, format_ip46_address, &peer0->ohc_ip, IP46_TYPE_ANY);
      peer0->retry_timer.base += peer0->retry_timer.period;
      upf_pfcp_server_timer_start (GTPU_ECHO_REQ_T1, id, &peer0->retry_timer);

      upf_gtp_echo_req (peer0, id);
    }
  else
    {
      upf_debug ("GTPu echo req fail, send node report req! peer_index is %u, Remote IP:%U\n", id,
                 format_ip46_address, &peer0->ohc_ip, IP46_TYPE_ANY);
      upf_node_assoc_t *n;

      u64 flags = NRT_USER_PLANE_PATH_FAILURE_REPORT;

      if (KEY_LOG_SWITCH(KL_SWITCH_GTPU_PATH_QOS_REPORT)) /* neil.fan@20231018 add: temporally ignore the conditions, need gtp path qos monitor trigger. */
      {
          flags |= NRT_GTP_U_PATH_QOS_REPORT;
      }

      peer0->path_failure = true;
      /* *INDENT-OFF* */
      pool_foreach (n, gtm->nodes, ({
                      upf_server_send_node_report_req (flags, n - gtm->nodes, peer0);
                    }));
      /* *INDENT-ON* */

      if (peer0->alarm_state == UPF_ALARM_TYPE_RECOVER)
      {
        /* *ALARM-OFF* */
        upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(ip46_address_t), CLIB_CACHE_LINE_BYTES);
        memset (upf_alarm->data, 0, sizeof (ip46_address_t));
        upf_alarm->alarm_id = UPF_ALARM_GTP_TUNNEL_TIMEOUT;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
        memcpy(upf_alarm->data, &peer0->ohc_ip, sizeof(ip46_address_t));
        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }
        /* *ALARM-ON* */
        peer0->alarm_state = UPF_ALARM_TYPE_PRODUCE;
      }
    }
}

static void
upf_pfcp_server_request_send (sx_msg_t *msg)
{
  upf_main_t *gtm = &g_upf_main;
  sx_enqueue_request (msg, gtm->pfcp_retry_n1, gtm->pfcp_t1_timeout);
  upf_send_pfcp_data (msg);
}

void
upf_pfcp_server_session_request_send (upf_session_t *sx, u8 type,
                                      struct pfcp_group *grp)
{
  sx_msg_t *msg;

  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics;
  //n4_statistics->sess_report.req_times++;
  //n4_statistics->report_type.usar++;

  if ((msg = sx_session_msg_build (sx, type, grp)))
    {
#if 0
      if (sx->user_id.flags & USER_ID_IMSI)
        upf_log_ex ("imsi: %s code:%U\n", sx->user_id.imsi_str, format_hex,
                    &msg->hdr->session_hdr,
                    clib_net_to_host_u16 (msg->hdr->length));
#endif
      // upf_debug ("Msg: %p\n", msg);
      upf_pfcp_server_request_send (msg);
    }
}

static void
upf_pfcp_server_node_request_send (upf_node_assoc_t *n, u8 type,
                                   struct pfcp_group *grp)
{
  sx_msg_t *msg;

  if ((msg = sx_node_msg_build (n, type, grp)))
    {
      // upf_debug ("Node Msg: %p\n", msg);
      upf_pfcp_server_request_send (msg);
    }
}

static void
response_time_expired (u32 id)
{
  sx_server_main_t *sxsm = &sx_server_main;
  clib_bihash_kv_40_8_t kv40;

  CHECK_POOL_IS_VALID_NORET(sxsm->msg_pool, id);
  sx_msg_t *msg = pool_elt_at_index (sxsm->msg_pool, id);

  upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d seq %u idx %u\n",
             msg->fib_index, format_ip46_address, &msg->lcl.address,
             IP46_TYPE_ANY, clib_net_to_host_u16 (msg->lcl.port),
             format_ip46_address, &msg->rmt.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->rmt.port), msg->seq_no, id);

  clib_memset (&kv40, 0, sizeof (kv40));
  clib_memcpy (kv40.key, msg->request_key, 32);
  clib_bihash_add_del_40_8 (&sxsm->response_q, &kv40, 0 /* is_del*/);
  upf_pfcp_server_timer_stop (msg->timer.handle);
  sx_msg_free (sxsm, msg);
}

static void
reset_response_timer (sx_msg_t *msg)
{
  sx_server_main_t *sxsm = &sx_server_main;
  u32 id = msg - sxsm->msg_pool;

  upf_debug ("Msg Seq No: %u, idx %u\n", msg->seq_no, id);

  upf_pfcp_server_timer_stop (msg->timer.handle);
  msg->timer.base += msg->timer.period;
  upf_pfcp_server_timer_start (PFCP_SERVER_RESPONSE, id, &msg->timer);
}

#if 0
static void
enqueue_response (sx_msg_t *msg)
{
  sx_server_main_t *sxsm = &sx_server_main;
  u32 id = msg - sxsm->msg_pool;
  clib_bihash_kv_40_8_t kv;

  upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d seq %u msg %u\n",
             msg->fib_index, format_ip46_address, &msg->lcl.address,
             IP46_TYPE_ANY, clib_net_to_host_u16 (msg->lcl.port),
             format_ip46_address, &msg->rmt.address, IP46_TYPE_ANY,
             clib_net_to_host_u16 (msg->rmt.port), msg->seq_no, id);

  clib_memset (&kv, 0, sizeof (kv));
  clib_memcpy (kv.key, msg->request_key, 32);
  kv.value = id;
  clib_bihash_add_del_40_8 (&sxsm->response_q, &kv, 1 /* is_add */);

  msg->timer.base = unix_time_now ();
  msg->timer.period = RESPONSE_TIMEOUT;
  upf_pfcp_server_timer_start (PFCP_SERVER_RESPONSE, id, &msg->timer);
}
#endif

static void
upf_pfcp_response_make (sx_msg_t *resp, sx_msg_t *req, size_t len)
{
  memset (resp, 0, sizeof (*resp));

  memset (&resp->timer, ~0, sizeof (resp->timer));
  resp->seq_no = req->seq_no;
  resp->fib_index = req->fib_index;
  resp->lcl = req->lcl;
  resp->rmt = req->rmt;
  vec_alloc (resp->data, len);
}

int
upf_pfcp_response_send (sx_msg_t *req, u64 cp_seid, u8 type,
                        struct pfcp_group *grp)
{
  sx_server_main_t *sxsm = &sx_server_main;
  sx_msg_t *resp;
  u32 msg_index;
  int r = 0;
  u32 length = 0;
  struct pfcp_response *response;

  r = upf_pfcp_msg_length (type, grp, &length);
  if (r != 0)
    {
      return r;
    }
  length = (length + PFCP_MSG_LENGTH_ALIGN - 1) & ~(PFCP_MSG_LENGTH_ALIGN - 1);

  msg_index = upf_msg_pool_instance_alloc ();
  CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, 0);
  resp = pool_elt_at_index (sxsm->msg_pool, msg_index);

  upf_pfcp_response_make (resp, req, length);

  resp->hdr->version = req->hdr->version;
  resp->hdr->s_flag = req->hdr->s_flag;
  resp->hdr->type = type;

  if (req->hdr->s_flag)
    {
      if (req->hdr->mp_flag)
        {
          resp->hdr->mp_flag = 1;
          resp->hdr->session_hdr.priority = req->hdr->session_hdr.priority;
        }

      resp->hdr->s_flag = 1;
      resp->hdr->session_hdr.seid = clib_host_to_net_u64 (cp_seid);

      memcpy (resp->hdr->session_hdr.sequence, req->hdr->session_hdr.sequence,
              sizeof (resp->hdr->session_hdr.sequence));
      _vec_len (resp->data) = offsetof (pfcp_header_t, session_hdr.ies);
    }
  else
    {
      memcpy (resp->hdr->msg_hdr.sequence, req->hdr->msg_hdr.sequence,
              sizeof (resp->hdr->session_hdr.sequence));
      _vec_len (resp->data) = offsetof (pfcp_header_t, msg_hdr.ies);
    }

  r = upf_pfcp_encode_msg (type, grp, &resp->data);
  if (r != 0)
    {
      vec_free (resp->data);
      upf_msg_pool_instance_free (msg_index);
      goto out_free;
    }

  /* vector resp might have changed */
  resp->hdr->length = clib_host_to_net_u16 (vec_len (resp->data) - 4);

  memcpy(&resp->user_id, &req->user_id, sizeof(resp->user_id));
  resp->user_id.nai = NULL;

  response = (struct pfcp_response *)((u8 *)grp + sizeof(struct pfcp_group));
  resp->cause = response->cause;

  resp->s_nssai = req->s_nssai;
  resp->dnn = format (resp->dnn, "%s", req->dnn);

  upf_send_pfcp_data (resp);
  // enqueue_response (resp);
  sx_msg_free (sxsm, resp);

out_free:
  upf_pfcp_free_msg (type, grp);
  return 0;
}

static int
urr_counter_check (u64 bytes, u64 consumed, u64 threshold, u64 quota,
                   upf_urr_t *urr)
{
  u32 r = 0;

  if (quota != 0 && consumed >= quota && !(urr->status & URR_OVER_QUOTA))
    {
      clib_atomic_fetch_or (&urr->status, URR_OVER_QUOTA);
#if 0 /*just send once report*/
      if (!(urr->status & URR_REACHED_THRESHOLD))
#endif
      r |= USAGE_REPORT_TRIGGER_VOLUME_QUOTA;
    }

  if (threshold != 0 && bytes >= threshold && !(urr->status & URR_REACHED_THRESHOLD))
    {
      clib_atomic_fetch_or (&urr->status, URR_REACHED_THRESHOLD);
      r |= USAGE_REPORT_TRIGGER_VOLUME_THRESHOLD;
    }

  return r;
}

static void
upf_pfcp_session_report_linked_usage (upf_session_t *sess,
                                      upf_urr_t **vec_reporting_urr,
                                      upf_urr_t **vec_urr_with_linked_urr,
                                      f64 now, pfcp_usage_report_t **report)
{
  upf_debug ("enter.\n");

  upf_urr_t *reporting_urr = NULL;
  upf_urr_t *urr_with_linked_urr = NULL;
  u32 *linked_urr_id = 0;
  u32 i, j;

  vec_foreach_index (i, vec_reporting_urr)
  {
    reporting_urr = vec_elt (vec_reporting_urr, i);

    vec_foreach_index (j, vec_urr_with_linked_urr)
    {
      urr_with_linked_urr = vec_elt (vec_urr_with_linked_urr, j);

      upf_debug (
          "i=%u, j=%u, reporting_urr->id=%u, urr_with_linked_urr->id=%u.\n", i,
          j, reporting_urr->id, urr_with_linked_urr->id);

      vec_foreach (linked_urr_id, urr_with_linked_urr->linked_urr_ids)
      {
        upf_debug ("*linked_urr_id=%u.\n", *linked_urr_id);

        if (reporting_urr->id != *linked_urr_id)
          {
            continue;
          }

        upf_debug ("call upf_usage_report_build() to build linked usage reporting "
                   "msg.\n");

        upf_usage_report_build (sess, urr_with_linked_urr,
                            USAGE_REPORT_TRIGGER_LINKED_USAGE_REPORTING, now,
                            report, UPF_INVALID_PDR);
        break;
      }
    }
  }

  return;
}

/* To find those urrs who link this urr id. */
u32 upf_pfcp_linked_urr_list_found (struct rules *active, u32 target_urr_id, upf_urr_t ***linked_list)
{
    upf_urr_t *urr;
    vec_foreach (urr, active->urr)
    {
        u32 *linked_urr_id;
        vec_foreach (linked_urr_id, urr->linked_urr_ids)
          {
            if (target_urr_id == *linked_urr_id)
                vec_add1 ((*linked_list), urr);
          }
    }

    return vec_len(*linked_list) ? 1 : 0;
}

static clib_error_t *
iupf_urr_linked_accumulate_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
  {
    if (unformat (input, "on"))
      {
        g_urr_linked_accumulate_switch = SWITCH_ON;
      }
    else if (unformat (input, "off"))
      {
        g_urr_linked_accumulate_switch = SWITCH_OFF;
      }
    else if (unformat (input, "show"))
      {
        vlib_cli_output (vm, "switch is [%s]\n", g_urr_linked_accumulate_switch == SWITCH_ON ? "on" : "off");
        return 0;
      }
    else
      return 0;
  }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_urr_linked_accumulate_command, static) = {
    .path = "upf urr linked accumulate switch",
    .short_help = "upf urr linked accumulate switch[on | off | show]",
    .function = iupf_urr_linked_accumulate_command_fn,
};
/* *INDENT-OFF* */

void upf_pfcp_linked_urr_usage_accumulated (upf_urr_t *urr, upf_urr_t **linked_list)
{
  if (g_urr_linked_accumulate_switch == SWITCH_OFF)
    return;

  upf_urr_t **link_urr;
  vec_foreach (link_urr, linked_list)
    {
      if (urr->volume.threshold.fields & PFCP_VOLUME_VOLUME)
        {
          link_urr[0]->volume.measure.bytes.dl += urr->volume.measure.bytes.dl;
          link_urr[0]->volume.measure.bytes.ul += urr->volume.measure.bytes.ul;
          link_urr[0]->volume.measure.bytes.total += urr->volume.measure.bytes.total;
        }

      if (urr->volume.quota.fields & PFCP_VOLUME_VOLUME)
        {
          link_urr[0]->volume.measure.consumed.dl += urr->volume.measure.consumed.dl;
          link_urr[0]->volume.measure.consumed.ul += urr->volume.measure.consumed.ul;
          link_urr[0]->volume.measure.consumed.total += urr->volume.measure.consumed.total;
        }
    }
}

static void
upf_pfcp_session_report_usage (upf_session_t *sx, f64 now, u32 pdr_index)
{
  pfcp_session_report_request_t req;
  struct rules *active;
  upf_urr_t *urr;
  upf_node_assoc_t *assoc = NULL;

  /* Andy added */
  upf_urr_t **vec_reporting_urr = NULL;
  upf_urr_t **vec_urr_with_linked_urr = NULL;

  active = upf_get_rules (sx, SX_ACTIVE);

  // upf_debug ("Active: %p (%d)\n", active, vec_len (active->urr));

  if (vec_len (active->urr) == 0) /* how could that happen? */
    return;

  if (g_upf_dnn_switch)
  {
      u32 hit = 0;
      upf_dnn_t *dnn = NULL;
      upf_main_t *gtm = &g_upf_main;
      vec_foreach (dnn, gtm->dnn)
      {
        if (vec_is_equal (dnn->name, sx->dnn))
          {
            hit = 1;
            break;
          }
      }
      if (hit)
      {
        if (dnn->urr_report_switch)
        {
            return;
        }
      }
  }

  if (g_upf_s_nssai_switch)
  {
      upf_s_nssai_t *nssai = upf_s_nssai_lookup(sx->s_nssai);
      if (nssai && nssai->urr_report_switch)
      {
         return;
      }
  }

  memset (&req, 0, sizeof (req));
  if (!pool_is_free_index (g_upf_main.nodes, sx->assoc.node))
    assoc = pool_elt_at_index (g_upf_main.nodes, sx->assoc.node);

  if (assoc && assoc->cp_feature & F_CPFF_LOAD)
    {

      if (upf_pfcp_load_info_report (&req.load_control_information))
        SET_BIT (req.grp.fields,
                 SESSION_REPORT_REQUEST_LOAD_CONTROL_INFORMATION);
    }
  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
  req.report_type = REPORT_TYPE_USAR;

  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_USAGE_REPORT);

  vec_foreach (urr, active->urr)
  {
    u32 trigger = 0;

    // upf_debug ("URR: %p\n", urr);

    trigger = urr_check_vol (urr->volume, ul, urr);
    trigger |= urr_check_vol (urr->volume, dl, urr);
    trigger |= urr_check_vol (urr->volume, total, urr);

    if ((urr->status & URR_TRAFFIC) && !urr->traffic.first_seen)
      {
        if (urr->triggers & REPORTING_TRIGGER_START_OF_TRAFFIC)
          trigger |= USAGE_REPORT_TRIGGER_START_OF_TRAFFIC;
        else
          urr->traffic.first_seen = now;
      }

    if (trigger != 0)
      {
        upf_urr_t **linked_list = NULL;
        if (upf_pfcp_linked_urr_list_found (active, urr->id, &linked_list))
          {
            if (trigger & (USAGE_REPORT_TRIGGER_VOLUME_THRESHOLD | USAGE_REPORT_TRIGGER_VOLUME_QUOTA))
              upf_pfcp_linked_urr_usage_accumulated(urr, linked_list);
            vec_free (linked_list);
          }

        upf_usage_report_build (sx, urr, trigger, now, &req.usage_report, pdr_index);

        /* Andy added, record current reporting urr id */
        vec_add1 (vec_reporting_urr, urr);
      }

    /* Andy added, record current urr id if with  LIUSA*/
    if ((urr->triggers & REPORTING_TRIGGER_LINKED_USAGE_REPORTING) &&
        (0 != vec_len (urr->linked_urr_ids)))
      {
        upf_debug ("urr id=%u with linked urr id.\n", urr->id);
        vec_add1 (vec_urr_with_linked_urr, urr);
      }

      if (g_upf_urr_flow_statistics_switch)
      {
          int k = 0, hit = 0;
          for (k = 0; k < UPF_U8_MAX_SIZE; k++)
          {
              if (g_upf_urr_flow_statistics[k].is_used && g_upf_urr_flow_statistics[k].urr_id == urr->id)
              {
                  hit = 1;
                  g_upf_urr_flow_statistics[k].volume.bytes.ul += urr->volume.measure.bytes.ul;
                  g_upf_urr_flow_statistics[k].volume.bytes.dl += urr->volume.measure.bytes.dl;
                  g_upf_urr_flow_statistics[k].volume.bytes.total += urr->volume.measure.bytes.total;
                  break;
              }
          }

          if (hit)
          {
              hit = 0;
          }
          else
          {
              for (k = 0; k < UPF_U8_MAX_SIZE; k++)
              {
                  if (g_upf_urr_flow_statistics[k].is_used == 0)
                  {
                      g_upf_urr_flow_statistics[k].is_used = 1;
                      g_upf_urr_flow_statistics[k].urr_id = urr->id;
                      g_upf_urr_flow_statistics[k].volume.bytes.ul = urr->volume.measure.bytes.ul;
                      g_upf_urr_flow_statistics[k].volume.bytes.dl = urr->volume.measure.bytes.dl;
                      g_upf_urr_flow_statistics[k].volume.bytes.total = urr->volume.measure.bytes.total;
                      break;
                  }
              }
          }
      }
  }

  /* Andy add : linked usage report process. */
  if (0 != vec_len (vec_urr_with_linked_urr))
    {
      upf_debug ("vec_urr_with_linked_urr len=%u.\n",
                 vec_len (vec_urr_with_linked_urr));
      upf_pfcp_session_report_linked_usage (sx, vec_reporting_urr,
                                            vec_urr_with_linked_urr, now,
                                            &req.usage_report);
    }

  if (vec_len (req.usage_report) != 0)
    upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST,
                                          &req.grp);

  upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);
  if (req.usage_report)
    vec_free (req.usage_report);

  /* Andy added, free vector  */
  if (0 != vec_len (vec_reporting_urr))
    {
      vec_free (vec_reporting_urr);
    }

  if (0 != vec_len (vec_urr_with_linked_urr))
    {
      vec_free (vec_urr_with_linked_urr);
    }
}

void
upf_pfcp_session_urr_time_stop (urr_time_t *t)
{
  upf_per_pfcp_thread_t *per_pfcp;

  per_pfcp = upf_get_per_pfcp ();

  if (t->handle != ~0)
    {
      // stop timer ....
      tw_timer_stop_1t_3w_1024sl_ov (&per_pfcp->timer, t->handle);
      t->handle = ~0;
    }
}


/*
 * [base]     [now]                        [timeout]
 *   |----------|------------------------------|
 *   |<---t1--->|<-------------t2------------->|
 *   |<----------------period----------------->|
 */
u32 upf_pfcp_timer_reset(u32 cb_index, u8 timer_id, urr_time_t *t, u8 status)
{
  /* the timer interval must be based on tw->current_tick, so for calculating
   * that we need to use the now timestamp of that current_tick */
  const f64 now = unix_time_now ();
  u32 value = (timer_id << 24) | (cb_index & 0xFFFFFF);
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (t->handle != ~0 && (status & TIMER_STOP))
    upf_pfcp_session_urr_time_stop (t);

  if (status & TIMER_ADVANCE)
    t->base += t->period;

  if (t->period != 0 && (status & TIMER_START))
    {
      f64 t1 = ceil ((now - t->base) * TW_CLOCKS_PER_SECOND);
      i64 t2 = t->period * TW_CLOCKS_PER_SECOND - t1;
      t2 = clib_max (t2, 1); /* make sure interval is at least 1 */
      t->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, value, 0, t2);
      upf_trace ("cb_index:%u, period:%us, base:%.3f, now:%.3f, t1:%.4f, t2:%lu, t2':%.4fs, current_tick:%u, handle:%d",
                 cb_index, t->period, t->base, now, t1, t2, ((t->base + (f64)t2) - now) * TW_CLOCKS_PER_SECOND,
                 per_pfcp->timer.current_tick, t->handle);
    }
  return 0;
}

void
upf_pfcp_session_urr_time_start_stop (u32 si, urr_time_t *t, u8 start_it,
                                      u8 need_stop)
{
  upf_per_pfcp_thread_t *per_pfcp;

  per_pfcp = upf_get_per_pfcp ();

  /* the timer interval must be based on tw->current_tick, so for calculating
   * that we need to use the now timestamp of that current_tick */
  const f64 now = unix_time_now ();

  if (t->handle != ~0 && need_stop)
    upf_pfcp_session_urr_time_stop (t);

  if (t->period != 0 && start_it)
    {
      i64 interval;

      // start timer.....

      interval = t->period * TW_CLOCKS_PER_SECOND -
                 ceil ((now - t->base) * TW_CLOCKS_PER_SECOND);
      interval = clib_max (interval, 1); /* make sure interval is at least 1 */
      t->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, si, PFCP_URR_TIMER, interval);

      upf_main_t *gtm = &g_upf_main;
      u64 up_seid = 0;
      if (!pool_is_free_index (gtm->sessions, si))
      {
        upf_session_t *sx = pool_elt_at_index (gtm->sessions, si);
        up_seid = sx->up_seid;
      }

      upf_trace ("upseid 0x%lx starting URR timer, now is %.3f, base is %.3f, "
                 "expire in %us,"
                 "%lu ticks,"
                 " alternate %.4f, %.4f, clib_now %.4f, current tick: "
                 "%u,handle=%d,per pfcp=%p",
                 up_seid, now, t->base, t->period, interval,
                 ((t->base + (f64)interval) - now) * TW_CLOCKS_PER_SECOND,
                 ((t->base + interval) - now) * TW_CLOCKS_PER_SECOND,
                 unix_time_now (), per_pfcp->timer.current_tick, t->handle,
                 per_pfcp);
    }
}

void
upf_pfcp_session_urr_time_abs_start_stop (u32 si, urr_time_t *t)
{
  upf_per_pfcp_thread_t *per_pfcp;

  per_pfcp = upf_get_per_pfcp ();

  /* the timer interval must be based on tw->current_tick, so for calculating
   * that we need to use the now timestamp of that current_tick */
  f64 now = unix_time_now ();

  if (t->handle != ~0)
    upf_pfcp_session_urr_time_stop (t);

  if (t->base != 0 && t->base > now)
    {
      u64 ticks;

      // start timer.....
      ticks = ceil ((t->base - now) * TW_CLOCKS_PER_SECOND) + 1;
      t->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, si, PFCP_URR_TIMER, ticks);

      upf_debug ("starting URR absolute timer %u, now is %.3f, base is %.3f, "
                 "expire in "
                 "%lu ticks\n",
                 si, now, t->base, ticks);
    }
}

void upf_mac_address_removed_append(upf_report_mac_information_t *report_info, upf_mac_addr_detect_t *t)
{
    pfcp_mac_addresses_t *addr;
    vec_foreach(addr, report_info->addr)
    {
        if ((addr->c_tag.tci == t->mac_info.c_tag) && (addr->s_tag.tci == t->mac_info.s_tag))
        {
            mac_address_t *mac;
            vec_foreach(mac, addr->macs)
            {
                if (mac_address_equal(mac, &t->mac_info.mac))
                {
                    return;
                }
            }
            vec_add1(addr->macs, t->mac_info.mac);
            return;
        }
    }

    report_info->pdr_idx = t->pdr_index;

    APPEND_NEW_MEMBER(report_info->addr, addr);
    addr->c_tag.tci = t->mac_info.c_tag;
    addr->c_tag.mask = VLAN_MASK_VID;
    addr->s_tag.tci = t->mac_info.s_tag;
    addr->s_tag.mask = VLAN_MASK_VID;
    vec_add1(addr->macs, t->mac_info.mac);
}

void upf_mac_address_removed_report(upf_session_t *sess, upf_report_mac_information_t *report_info)
{
    upf_main_t *um = &g_upf_main;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    msg->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_report_mac_information_t), CLIB_CACHE_LINE_BYTES);
    memset (msg->data, 0, sizeof(upf_report_mac_information_t));
    msg->msg_id = PFCP_RPC_PUBLISH_REPORT_MAC_ADDRESS_REPORT;

    report_info->sess_idx = sess - um->sessions;
    report_info->is_detect = 0;
    clib_memcpy(msg->data, report_info, sizeof(*report_info));

    for (int i = 0; i < um->num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_REPORT_MAC_ADDRESS_REPORTED,\n");
        send_sx_msg_to_pfcp_thread(i, msg);
    }
}

void upf_traffic_inactive_detect_report(upf_session_t *sx, upf_urr_t *urr)
{
    upf_main_t *um = &g_upf_main;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    msg->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_inner_report_info_t), CLIB_CACHE_LINE_BYTES);
    memset (msg->data, 0, sizeof(upf_inner_report_info_t));
    msg->msg_id = PFCP_RPC_PUBLISH_TRAF_INACT_DETECT_REPORT;

    upf_inner_report_info_t *p = msg->data;
    p->sess_idx = sx - um->sessions;
    if (urr->traf_inact_detect.has_pdr)
    {
        p->pdr_id = urr->traf_inact_detect.pdr_id;
        p->has_pdrid = 1;
    }
    p->urr_id = urr->id;
    p->traf_inact_detect.type = urr->traf_inact_detect.type;

    for (int i = 0; i < um->num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_TRAF_INACT_DETECT_REPORT,\n");
        send_sx_msg_to_pfcp_thread(i, msg);
    }
}

u32 upf_pfcp_per_qos_monitor_timer_handle (upf_per_qos_flow_control_t *ctrl, upf_per_qos_monitor_flow_control_t *flow_ctrl,
                                           upf_monitor_send_timer_t *timer, f64 now, u32 sx_idx, u8 trigger_flag)
{
    if (urr_check_time (timer->tx_trigger, now))
    {
        upf_pfcp_monitor_send_timer_reset(timer, now, sx_idx, TIMER_START | TIMER_ADVANCE);
        return 0;
    }

    pfcp_qfi_t qfi = flow_ctrl->qfi;

    if ((urr_check_time (timer->fail_report, now)) && timer->measuring && timer->send_cnt && !timer->recv_cnt && !timer->is_report)
    {
        timer->measuring = 0; /* measure end */
        if (trigger_flag == QMP_TRIG_DEFAULT)
        {
            upf_64bit_timestamp_t t;
            upf_time_now_nsec_fraction (&t);
            if (t.sec <= ctrl->minimum_wait_time.min_wait_time + flow_ctrl->default_report.wait_timestamp)
                return 0;
            flow_ctrl->default_report.wait_timestamp = t.sec;
        }
        timer->is_report = 1;
        upf_debug("per qos monitor measure failure report, sx_idx:%u, qfi:%u", sx_idx, qfi);
        return 1;
    }

    if ((urr_check_time (timer->dummy_gtp, now)) && timer->measuring && !timer->send_cnt)
    {
        if (!upf_gtpu_send_qmp_dummy_gtp (qfi, sx_idx))
            timer->send_cnt = 1;
        else
        {
            timer->measuring = 0;
            upf_debug("send dummy failed, sx_idx:%u, qfi:%u", sx_idx, qfi);
        }
    }

    return 0;
}

u32 upf_pfcp_session_srr_timer (upf_session_t *sx, f64 now)
{
    pfcp_session_report_request_t req = {0};
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    u32 sx_idx = sx - g_upf_main.sessions;

    upf_debug ("###up_seid:0x%lx, now:%.3f\n", sx->up_seid, now);

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
    req.report_type = REPORT_TYPE_SESR;

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_SESSION_REPORT);

    upf_srr_t *srr;
    vec_foreach (srr, active->srr)
    {
        upf_per_qos_flow_control_t *per_qos_flow_ctrl;
        pfcp_session_report_t sx_report = {0};
        vec_foreach (per_qos_flow_ctrl, srr->per_qos_flow_ctrl)
        {
            upf_per_qos_monitor_flow_control_t *flow_ctrl;
            vec_foreach (flow_ctrl, per_qos_flow_ctrl->flow_ctrl)
            {
                u32 r = 0;
                if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD)
                    r = upf_pfcp_per_qos_monitor_timer_handle (per_qos_flow_ctrl, flow_ctrl, &flow_ctrl->periodic_report, now, sx_idx, QMP_TRIG_PERIOD);
                else if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_DEFAULT)
                    r = upf_pfcp_per_qos_monitor_timer_handle (per_qos_flow_ctrl, flow_ctrl, &flow_ctrl->default_report, now, sx_idx, QMP_TRIG_DEFAULT);
                if (r)
                {
                    pfcp_qos_monitoring_report_t qos_monitor_report = {0};
                    u8 reporting_frequency = IE_REPORTING_FREQUENCYE_PERIOD;
                    if (!(flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD))
                        reporting_frequency = IE_REPORTING_FREQUENCYE_EVENT;
                    pfcp_build_one_qos_monitor_report (&qos_monitor_report, flow_ctrl, reporting_frequency, now, NULL);
                    vec_add1 (sx_report.qos_monitor_report, qos_monitor_report);
                }
            }
        }

        if (sx_report.qos_monitor_report)
        {
            SET_BIT (sx_report.grp.fields, SESSION_REPORT_SRR_ID);
            sx_report.srr_id = srr->id;
            SET_BIT (sx_report.grp.fields, SESSION_REPORT_QOS_MONITORING);
            vec_add1 (req.session_report, sx_report);
        }
    }

    if (vec_len (req.session_report) != 0)
        upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp);

    upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);

    return 0;
}

static void
upf_pfcp_session_timer_of_urr (upf_session_t *sx, f64 now)
{
  upf_debug ("up_seid: 0x%lx, sx: %p, now: %.3f", sx->up_seid, sx, now);

  pfcp_session_report_request_t req;
  upf_main_t *gtm = &g_upf_main;
  struct rules *active;
  upf_urr_t *urr;
  upf_node_assoc_t *assoc = NULL;
  u32 pdr_index = UPF_INVALID_PDR;

  /* Andy added */
  upf_urr_t **vec_reporting_urr = NULL;
  upf_urr_t **vec_urr_with_linked_urr = NULL;

  active = upf_get_rules (sx, SX_ACTIVE);

  memset (&req, 0, sizeof (req));
  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);

  req.report_type = REPORT_TYPE_USAR;

  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_USAGE_REPORT);
  u32 si = sx - gtm->sessions;

  vec_foreach (urr, active->urr)
  {
    u32 trigger = 0;

#if 0 // debug
#define urr_debug(Label, t)                                                \
  upf_debug ("%-10s %20lu secs @ %U, in %9.3f secs (%9.3f  %9.3f), "       \
             "handle 0x%08x, check: %u",                                   \
             (Label), (t).period, /* VPP does not support ISO dates... */  \
             format_time_float, 0, (t).base + (f64) (t).period,            \
             ((f64) (t).period) - (now - (t).base),                        \
             (now - (t).base - (t).period) * TW_CLOCKS_PER_SECOND,         \
             trunc ((now - (t).base - (t).period) * TW_CLOCKS_PER_SECOND), \
             (t).handle, urr_check (t, now));

    urr_debug ("URR: %p, Id: %u",urr->id);
    urr_debug ("Period", urr->measurement_period);
    urr_debug ("Threshold", urr->time_threshold);
    urr_debug ("Quota", urr->time_quota);
    urr_debug ("Monitoring", urr->monitoring_time);
#endif
    if (urr_check_time (urr->measurement_period, now))
      {
        if (urr->triggers & REPORTING_TRIGGER_PERIODIC_REPORTING)
          trigger |= USAGE_REPORT_TRIGGER_PERIODIC_REPORTING;

        urr->measurement_period.base += urr->measurement_period.period;

        /* rearm Measurement Period */
        upf_pfcp_session_urr_time_start_stop (si, &urr->measurement_period, 1,
                                              0);
      }
    if (urr_check_time (urr->stop_of_traffic, now))
      {
        urr->stop_of_traffic.base += urr->stop_of_traffic.period;
        if (urr->status & URR_TRAFFIC)
          clib_atomic_fetch_and (&urr->status, ~URR_TRAFFIC);
        else if ((urr->triggers & REPORTING_TRIGGER_STOP_OF_TRAFFIC) &&
                 !urr->traffic.last_seen && urr->traffic.first_seen)
        {
          trigger |= USAGE_REPORT_TRIGGER_STOP_OF_TRAFFIC;
          upf_pdr_t *pdr = upf_get_pdr_by_id (active, urr->traffic.pdr_id);
          pdr_index = pdr ? (pdr - active->pdr) : UPF_INVALID_PDR;
        }
        upf_pfcp_session_urr_time_start_stop (si, &urr->stop_of_traffic, 1, 0);
      }
    if ((urr->update_flags & SX_URR_UPDATE_IDT) && (urr->idt.period != 0))
      {
        if (urr_check_time (urr->idt, now))
          {
            urr->idt.base += urr->idt.period;
            if (urr->status & URR_ACTIVE_TIME)
              {
                clib_atomic_fetch_and (&urr->status, ~URR_ACTIVE_TIME);

                if ((urr->update_flags & SX_URR_UPDATE_TIME_THRESHOLD) &&
                    (urr->methods & SX_URR_TIME))
                  {

                    urr->time_threshold.consumed += urr->idt.period;
                    if ((urr->time_threshold.consumed >
                         urr->time_threshold.total_time) &&
                        !(urr->status & URR_REACHED_THRESHOLD))
                      {
                        trigger |= USAGE_REPORT_TRIGGER_TIME_THRESHOLD;
                        clib_atomic_fetch_or (&urr->status,
                                              URR_REACHED_THRESHOLD);
                      }
                  }

                if ((urr->update_flags & SX_URR_UPDATE_TIME_QUOTA) &&
                    (urr->methods & SX_URR_TIME))
                  {
                    urr->time_quota.consumed += urr->idt.period;
                    if ((urr->time_quota.consumed >
                         urr->time_quota.total_time) &&
                        !(urr->status & URR_OVER_QUOTA))
                      {
                        trigger |= USAGE_REPORT_TRIGGER_TIME_QUOTA;
                        clib_atomic_fetch_or (&urr->status, URR_OVER_QUOTA);
                      }
                  }

                if (urr->update_flags & SX_URR_UPDATE_QUOTA_HOLDING_TIME)
                  {
                    urr->quota_holding_time.consumed +=
                        urr->quota_holding_time.period;
                    if (urr->quota_holding_time.consumed >
                        urr->quota_holding_time.total_time)
                      {
                        trigger |= USAGE_REPORT_TRIGGER_QUOTA_HOLDING_TIME;
                        urr->quota_holding_time.consumed = 0;
                      }
                  }
              }
            upf_pfcp_session_urr_time_start_stop (si, &urr->idt, 1, 0);
          }
      }
    else
      {
        if ((urr->update_flags & SX_URR_UPDATE_TIME_THRESHOLD) &&
            !(urr->status & URR_REACHED_THRESHOLD) &&
            urr_check_time (urr->time_threshold, now))
          {
            trigger |= USAGE_REPORT_TRIGGER_TIME_THRESHOLD;
            clib_atomic_fetch_or (&urr->status, URR_REACHED_THRESHOLD);
            urr->time_threshold.consumed += urr->time_threshold.period;
            if (!(urr->update_flags & SX_URR_UPDATE_TIME_QUOTA))
              { /*just for offline charging*/
                urr->time_threshold.base += urr->time_threshold.period;
                upf_pfcp_session_urr_time_start_stop (si, &urr->time_threshold,
                                                      1, 0);
              }
          }
        if ((urr->update_flags & SX_URR_UPDATE_TIME_QUOTA) &&
            !(urr->status & URR_OVER_QUOTA) &&
            urr_check_time (urr->time_quota, now))
          {
            urr->time_quota.consumed += urr->time_quota.period;
            trigger |= USAGE_REPORT_TRIGGER_TIME_QUOTA;
            clib_atomic_fetch_or (&urr->status, URR_OVER_QUOTA);
          }
      }

    if ((urr->update_flags & SX_URR_UPDATE_ETH_INACT_TIMER) && urr_check_time (urr->mac_detect, now))
    {
        upf_mac_addr_detect_t *t;
        upf_report_mac_information_t report_info = {0};
        vec_foreach (t, sx->mac_address_tag_vec)
        {
            if (t->is_active)
            {
                t->is_active = 0;
            }
            else
            {
                if (t->detect_is_reported)
                {
                    upf_mac_address_removed_append(&report_info, t);
                    t->detect_is_reported = 0;
                }
            }
        }

        if (report_info.addr)
            upf_mac_address_removed_report(sx, &report_info);

        urr->mac_detect.base += urr->mac_detect.period;
        upf_pfcp_session_urr_time_start_stop (si, &urr->mac_detect, 1, 0);
    }

    if ((urr->update_flags & SX_URR_UPDATE_TRAFFIC_INACT_TIMER) && urr_check_time (urr->traf_inact_detect_timer, now))
    {
        upf_urr_traf_inactive_detect_t *t = &urr->traf_inact_detect;
        if (t->is_active)
        {
            t->is_active = 0;
            t->has_reported = 0;
        }
        else
        {
            /* To avoid repeat reporting */
            if (!t->has_reported)
            {
                t->has_reported = 1;
                upf_traffic_inactive_detect_report(sx, urr);
            }
        }

        urr->traf_inact_detect_timer.base += urr->traf_inact_detect_timer.period;
        upf_pfcp_session_urr_time_start_stop (si, &urr->traf_inact_detect_timer, 1, 0);
    }

    if (trigger != 0)
      {
        upf_usage_report_build (sx, urr, trigger, now, &req.usage_report, pdr_index);

        // clear reporting on the time based triggers, until rearmed by update
        // urr->triggers &=
        //   ~(REPORTING_TRIGGER_TIME_THRESHOLD |
        //   REPORTING_TRIGGER_TIME_QUOTA);

        /* Andy added, record current reporting urr id */
        vec_add1 (vec_reporting_urr, urr);
      }
    else if (!(urr->status & URR_AFTER_MONITORING_TIME) &&
             (urr->monitoring_time.base != 0) &&
             (urr->monitoring_time.base <= ceil (now)))
      {
        urr_pfcp_lock (urr);

        urr->usage_before_monitoring_time.volume = urr->volume.measure;
        memset (&urr->volume.measure.packets, 0,
                sizeof (urr->volume.measure.packets));
        memset (&urr->volume.measure.bytes, 0,
                sizeof (urr->volume.measure.bytes));

        urr_pfcp_unlock (urr);
        urr->usage_before_monitoring_time.traffic = urr->traffic;
        memset (&urr->traffic, 0, sizeof (upf_urr_traffic_t));
        clib_atomic_fetch_and (&urr->status, ~URR_TRAFFIC);

        upf_pfcp_session_urr_time_stop (&urr->monitoring_time);
        if (urr->update_flags & SX_URR_UPDATE_SUB_TIME_QUOTA)
          urr->time_quota.total_time = urr->sub_time_quota.total_time;
        if (urr->update_flags & SX_URR_UPDATE_SUB_TIME_THRESHOLD)
          urr->time_threshold.total_time = urr->sub_time_threshold.total_time;
        if (urr->update_flags & SX_URR_UPDATE_SUB_VOL_THRESHOLD)
          urr->volume.threshold = urr->sub_volume.threshold;
        if (urr->update_flags & SX_URR_UPDATE_SUB_VOL_QUOTA)
          urr->volume.quota = urr->sub_volume.quota;

        urr->usage_before_monitoring_time.start_time = urr->start_time;
        urr->start_time = now;
        // urr->status |= URR_AFTER_MONITORING_TIME;
        clib_atomic_fetch_or (&urr->status, URR_AFTER_MONITORING_TIME);
      }

    /* Andy added, record current urr id if with  LIUSA*/
    if ((urr->triggers & REPORTING_TRIGGER_LINKED_USAGE_REPORTING) &&
        (0 != vec_len (urr->linked_urr_ids)))
      {
        upf_debug ("urr id=%u with linked urr id.\n", urr->id);
        vec_add1 (vec_urr_with_linked_urr, urr);
      }
  }

  /* Andy add : linked usage report process. */
  if (0 != vec_len (vec_urr_with_linked_urr))
    {
      upf_debug ("vec_urr_with_linked_urr len=%u.\n",
                 vec_len (vec_urr_with_linked_urr));
      upf_pfcp_session_report_linked_usage (sx, vec_reporting_urr,
                                            vec_urr_with_linked_urr, now,
                                            &req.usage_report);
    }

  if (vec_len (req.usage_report) != 0)
  {
      if (!pool_is_free_index (g_upf_main.nodes, sx->assoc.node))
        assoc = pool_elt_at_index (g_upf_main.nodes, sx->assoc.node);
      if (assoc && assoc->cp_feature & F_CPFF_LOAD)
        {
          if (upf_pfcp_load_info_report (&req.load_control_information))
            SET_BIT (req.grp.fields,
                     SESSION_REPORT_REQUEST_LOAD_CONTROL_INFORMATION);
        }
    upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp);
  }

  upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);

  /* Andy added, free vector  */
  if (0 != vec_len (vec_reporting_urr))
    {
      vec_free (vec_reporting_urr);
    }

  if (0 != vec_len (vec_urr_with_linked_urr))
    {
      vec_free (vec_urr_with_linked_urr);
    }
}

void
upf_pfcp_server_timer_stop (u32 handle)
{
  upf_per_pfcp_thread_t *per_pfcp;

  per_pfcp = upf_get_per_pfcp ();
  if (handle &&
      !tw_timer_handle_is_free_1t_3w_1024sl_ov (&per_pfcp->timer, handle))
    tw_timer_stop_1t_3w_1024sl_ov (&per_pfcp->timer, handle);
}

void
upf_pfcp_server_timer_start (u8 type, u32 id, pfcp_time_t *timer)
{
  i64 interval;
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (!timer->period)
    return;

  /* period is relative duration in ticks */
  interval = timer->period * TW_CLOCKS_PER_SECOND -
             floor ((unix_time_now () - timer->base) * TW_CLOCKS_PER_SECOND);

  interval = clib_max (interval, 1); /* make sure interval is at least 1 */

  ASSERT (type <= 255);
  ASSERT ((id & 0xff000000) == 0);
  timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, (type << 24) | id, PFCP_URR_TIMER, interval);
}

void
upf_oam_server_timer_start (u8 type, u32 id, oam_time_t *timer)
{
  i64 interval;
  upf_per_oam_thread_t *per_oam = upf_get_per_oam ();

  if (!timer->period)
    return;

  /* period is relative duration in ticks */
  interval = timer->period * TW_CLOCKS_PER_SECOND -
             floor ((unix_time_now () - timer->base) * TW_CLOCKS_PER_SECOND);

  interval = clib_max (interval, 1); /* make sure interval is at least 1 */

  ASSERT (type <= 255);
  ASSERT ((id & 0xff000000) == 0);
  timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_oam->timer, (type << 24) | id, PFCP_URR_TIMER, interval);
}

static int upf_handle_alarm_db_agming(BVT (clib_bihash_kv) * kvp, void *arg)
{
    int rv = 0;
    u64 current_time;
    u32 *alarm_type = (u32 *)arg;
    current_time = time(NULL);

    if (kvp->value != 0 && (current_time - kvp->value) > 10)
    {
        upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));
        upf_debug("######## wuwei key:%lu, value:%lu, current_time:%lu, alarm_type:%u\n", kvp->key, kvp->value, current_time, *alarm_type);
        if (*alarm_type == UPF_ALARM_UL_UEIP_CHECK_FAIL)
        {
            upf_ip_check_t *tmp = (upf_ip_check_t *)kvp->key;
            upf_alarm->alarm_id = UPF_ALARM_UL_UEIP_CHECK_FAIL;
            upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
            upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
            upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_ip_check_t), CLIB_CACHE_LINE_BYTES);
            memset (upf_alarm->data, 0, sizeof(upf_ip_check_t));
            memcpy(upf_alarm->data, tmp, sizeof(*tmp));
            for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
            {
                pfcp_send_sx_alarm_to_thread(i, upf_alarm);
            }
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, kvp, 0 /* is_add */ );
        }
        else if (*alarm_type == UPF_ALARM_DL_SESSION_CHECK_FAIL)
        {
            upf_pkt_ip_t *tmp = (upf_pkt_ip_t *)kvp->key;
            upf_debug("src_ip:%U, dst_ip:%U\n", format_ip4_address, &tmp->src_ip, format_ip4_address, &tmp->dst_ip);
            upf_alarm->alarm_id = UPF_ALARM_DL_SESSION_CHECK_FAIL;
            upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
            upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
            upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_pkt_ip_t), CLIB_CACHE_LINE_BYTES);
            memset (upf_alarm->data, 0, sizeof(upf_pkt_ip_t));
            memcpy(upf_alarm->data, tmp, sizeof(*tmp));
            for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
            {
                pfcp_send_sx_alarm_to_thread(i, upf_alarm);
            }
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, kvp, 0 /* is_add */ );
        }
        if (rv != 0)
        {
            upf_err("alarm bihash table del fail, key:%lu, value:%lu, current_time:%lu\n", kvp->key, kvp->value, current_time);
        }
    }

    return 1;
}

void
upf_alarm_timer_of_db_agming ()
{
    u32 alarm_type;

    alarm_type = UPF_ALARM_UL_UEIP_CHECK_FAIL;
    BV (clib_bihash_foreach_key_value_pair)
     ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, upf_handle_alarm_db_agming, &alarm_type);

    alarm_type = UPF_ALARM_DL_SESSION_CHECK_FAIL;
    BV (clib_bihash_foreach_key_value_pair)
     ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, upf_handle_alarm_db_agming, &alarm_type);

}

void upf_recv_echo_rsp (ip46_sd_t *ip46, icmp46_header_t *icmp, u8 is_ip4)
{
    upf_health_data_t *p = NULL;

    if (is_ip4)
    {
        p = &g_upf_main.health_gre4;
    }
    else
    {
        p = &g_upf_main.health_gre6;
    }
    if (p)
    {
        icmp46_echo_request_t *icmp46_echo = (icmp46_echo_request_t *)(icmp + 1);
        if (p->host_id == clib_net_to_host_u16(icmp46_echo->id))
        {
            upf_trace (" %U  host_id:%u rsp-id:%u", format_ip46_sd, ip46,
                p->host_id, clib_net_to_host_u16(icmp46_echo->id));
            p->recv_cnt = p->send_cnt;
        }
    }
}

void upf_input_next_obtain(vlib_buffer_t *b, u8 is_ip4, ip46_sd_t *ip46)
{
    if (is_ip4)
    {
        if (PREDICT_FALSE((ip4_address_is_multicast(&ip46->dst.ip4))
            || (ip4_address_is_global_broadcast(&ip46->dst.ip4))))
        {
            return;
        }

        ip4_header_t *ip4 = ip4 = vlib_buffer_get_current (b);
        if (PREDICT_FALSE(ip4->protocol == IP_PROTOCOL_ICMP))
        {
            icmp46_header_t *icmp = (icmp46_header_t *)(ip4 + 1);
            if (icmp->type == ICMP4_echo_reply)
                upf_recv_echo_rsp(ip46, icmp, is_ip4);
            return;
        }
    }
    else
    {
        if (PREDICT_FALSE((ip6_address_is_link_local_unicast(&ip46->dst.ip6)) || (ip6_address_is_local_unicast(&ip46->dst.ip6)) ||  (ip6_address_is_multicast(&ip46->dst.ip6))))
        {
            return;
        }

        ip6_header_t *ip6 = vlib_buffer_get_current (b);
        if (ip6->protocol == IP_PROTOCOL_IPV6_FRAGMENTATION) // fragment header for ipv6
        {
            return;
        }
        if (ip6 && ip6->protocol == IP_PROTOCOL_ICMP6)
        {
            icmp46_header_t *icmp = (icmp46_header_t *)(ip6 + 1);
            if (icmp->type == ICMP6_echo_reply)
                upf_recv_echo_rsp(ip46, icmp, is_ip4);
            return;
        }
    }

    return;
}

u16 ip4_icmp_compute_checksum (vlib_main_t * vm, vlib_buffer_t * p0, ip4_header_t * ip0)
{
    ip_csum_t sum0;
    u32 ip_header_length, payload_length_host_byte_order;
    u32 n_this_buffer, n_bytes_left, n_ip_bytes_this_buffer;
    u16 sum16;
    void *data_this_buffer;

    ip_header_length = ip4_header_bytes (ip0);
    payload_length_host_byte_order =
      clib_net_to_host_u16 (ip0->length) - ip_header_length;

    /* ICMP4 checksum does not include the IP header */
    sum0 = 0;

    n_bytes_left = n_this_buffer = payload_length_host_byte_order;
    data_this_buffer = (void *) ip0 + ip_header_length;
    n_ip_bytes_this_buffer =
      p0->current_length - (((u8 *) ip0 - p0->data) - p0->current_data);
    if (n_this_buffer + ip_header_length > n_ip_bytes_this_buffer)
      {
        n_this_buffer = n_ip_bytes_this_buffer > ip_header_length ?
      n_ip_bytes_this_buffer - ip_header_length : 0;
      }
    while (1)
      {
        sum0 = ip_incremental_checksum (sum0, data_this_buffer, n_this_buffer);
        n_bytes_left -= n_this_buffer;
        if (n_bytes_left == 0)
      break;

        ASSERT (p0->flags & VLIB_BUFFER_NEXT_PRESENT);
        p0 = vlib_get_buffer (vm, p0->next_buffer);
        data_this_buffer = vlib_buffer_get_current (p0);
        n_this_buffer = p0->current_length;
      }

    sum16 = ~ip_csum_fold (sum0);

    return sum16;
}

static void upf_ip46_fix_len_and_csum (vlib_main_t * vm, int l4_offset, u16 data_len, vlib_buffer_t * b0, int is_ip6)
{
    u16 payload_length = data_len + sizeof (icmp46_header_t) + offsetof (icmp46_echo_request_t, time_sent);
    u16 total_length = payload_length + l4_offset;
    icmp46_header_t *icmp46 = vlib_buffer_get_current (b0) + l4_offset;
    icmp46->checksum = 0;

    if (is_ip6)
    {
        ip6_header_t *ip6 = vlib_buffer_get_current (b0);
        ip6->payload_length = clib_host_to_net_u16 (payload_length);
        int bogus_length = 0;
        icmp46->checksum =
        ip6_tcp_udp_icmp_compute_checksum (vm, b0, ip6, &bogus_length);
    }
    else
    {
        ip4_header_t *ip4 = vlib_buffer_get_current (b0);
        ip4->length = clib_host_to_net_u16 (total_length);
        ip4->checksum = ip4_header_checksum (ip4);
        icmp46->checksum = ip4_icmp_compute_checksum (vm, b0, ip4);
    }
}

static int ip46_echo_fill_l3_header (ip46_address_t * pa46, vlib_buffer_t * b0, int is_ip6)
{
    if (is_ip6)
    {
        ip6_header_t *ip6 = vlib_buffer_get_current (b0);
        /* Fill in ip6 header fields */
        ip6->ip_version_traffic_class_and_flow_label =
        clib_host_to_net_u32 (0x6 << 28);
        ip6->payload_length = 0;  /* will be set later */
        ip6->protocol = IP_PROTOCOL_ICMP6;
        ip6->hop_limit = 255;
        ip6->dst_address = pa46->ip6;
        ip6->src_address = pa46->ip6;
        return (sizeof (ip6_header_t));
    }
    else
    {
        ip4_header_t *ip4 = vlib_buffer_get_current (b0);
        /* Fill in ip4 header fields */
        ip4->checksum = 0;
        ip4->ip_version_and_header_length = 0x45;
        ip4->tos = 0;
        ip4->length = 0;      /* will be set later */
        ip4->fragment_id = 0;
        ip4->flags_and_fragment_offset = 0;
        ip4->ttl = 0xff;
        ip4->protocol = IP_PROTOCOL_ICMP;
        ip4->src_address = pa46->ip4;
        ip4->dst_address = pa46->ip4;
        return (sizeof (ip4_header_t));
    }
}

u32 upf_send_echo_req (upf_health_data_t *health)
{
    vlib_main_t *vm = vlib_get_main ();
    u32 bi = 0;

    if (vlib_buffer_alloc (vm, &bi, 1) != 1)
    {
        return 1;
    }

    vlib_buffer_t *b0 = vlib_get_buffer (vm, bi);
    VLIB_BUFFER_TRACE_TRAJECTORY_INIT (b0);
    int is_ip6 = !ip46_address_is_ip4(&health->ip);
    u32 table_id = 0;
    u32 fib_index;
    if (is_ip6)
        fib_index = ip6_fib_index_from_table_id (table_id);
    else
        fib_index = ip4_fib_index_from_table_id (table_id);
    u32 sw_if_index = ~0;

    if (fib_index != ~0)
    {
        fib_node_index_t fib_entry_index = is_ip6 ?
            ip6_fib_table_lookup (fib_index, &health->ip.ip6, 128) :
            ip4_fib_table_lookup (ip4_fib_get (fib_index), &health->ip.ip4, 32);
        sw_if_index = fib_entry_get_resolving_interface (fib_entry_index);
    }

    if (sw_if_index == ~0)
    {
        vlib_buffer_free (vm, &bi, 1);
        return 2;
    }

    vnet_buffer (b0)->sw_if_index[VLIB_RX] = sw_if_index;
    vnet_buffer (b0)->sw_if_index[VLIB_TX] = fib_index;

    int l4_header_offset = ip46_echo_fill_l3_header (&health->ip, b0, is_ip6);
    if (is_ip6)
    {
        ip6_header_t *ip6 = vlib_buffer_get_current (b0);
        if (!ip6_src_address_for_packet (sw_if_index, &ip6->dst_address, &ip6->src_address))
        {
            vlib_buffer_free (vm, &bi, 1);
            return 3;
        }
    }
    else
    {
        ip4_main_t *im = &ip4_main;
        ip4_header_t *ip4 = vlib_buffer_get_current (b0);
        if (ip4_src_address_for_packet (&im->lookup_main, sw_if_index, &ip4->src_address))
        {
            vlib_buffer_free (vm, &bi, 1);
            return 3;
        }
    }

    icmp46_header_t *icmp46 = vlib_buffer_get_current (b0) + l4_header_offset;
    icmp46->type = is_ip6 ? ICMP6_echo_request : ICMP4_echo_request;
    icmp46->code = 0;
    icmp46->checksum = 0;
    u16 data_len = l4_header_offset + sizeof(icmp46_header_t);

    icmp46_echo_request_t *icmp46_echo = (icmp46_echo_request_t *) (icmp46 + 1);
    icmp46_echo->id = clib_host_to_net_u16 (health->host_id);
    icmp46_echo->seq = clib_host_to_net_u16 (health->send_cnt);
    //icmp46_echo->time_sent = clib_cpu_time_now ();
    data_len += offsetof (icmp46_echo_request_t, time_sent);
    clib_memcpy(icmp46_echo->data, pad_data, sizeof(pad_data));
    data_len += sizeof(pad_data);

    b0->current_length = data_len;

    upf_ip46_fix_len_and_csum (vm, l4_header_offset, sizeof(pad_data), b0, is_ip6);

    /* Enqueue the packet right now */
    u32 next_index = is_ip6 ? ip6_lookup_node.index : ip4_lookup_node.index;
    vlib_frame_t *f = vlib_get_frame_to_node (vm, next_index);
    u32 *to_next = vlib_frame_vector_args (f);
    to_next[0] = bi;
    f->n_vectors = 1;
    vlib_put_frame_to_node (vm, next_index, f);

    return 0;
}

void upf_health_check(u32 is_gre4)
{
    upf_health_data_t *p = NULL;
    u32 ret = 0;

    if (g_upf_main.dataguard_switch == DATAGUARD_SWITCH_OFF)
        return;

    if (is_gre4)
    {
        p = &g_upf_main.health_gre4;
    }
    else
    {
        p = &g_upf_main.health_gre6;
    }

    /* 1) send echo req */
    ret = upf_send_echo_req(p);
    if (ret == 0)
    {
        upf_debug ("megw_send_echo_req success.\n");
    }
    else if (ret == 1)
    {
        upf_debug ("vlib_buffer_alloc fail.\n");
    }
    else if (ret == 2)
    {
        upf_debug ("get sw_if_index fail.\n");
    }
    else if (ret == 3)
    {
        upf_debug ("ip4/ip6 src address set fail.\n");
    }
    else
    {
        upf_debug ("invalid ret.\n");
    }
    p->send_cnt++;

    /* 2) refresh state */
    int delta = clib_abs(p->send_cnt - p->recv_cnt);
    if (delta <= 1)
    {
        g_upf_main.flow_to_gre = 1;
    }
    else if (delta > 3)
    {
        g_upf_main.flow_to_gre = 0;
    }

    pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = 2;
    upf_pfcp_server_timer_start (UPF_ECHO_REQ_TIMER, is_gre4, &timer);
}

f64 upf_get_total_cpu_usage()
{
    u32 thread_num = 0;
    f64 total_cpu_usage = 0;

    for (int i = 0; i < UPF_VPP_CORE_MAX; i++)
    {
        if (g_upf_cpu_usage[i].active)
        {
            total_cpu_usage += g_upf_cpu_usage[i].cpu_usage;
            thread_num++;
        }
    }

    return total_cpu_usage/thread_num;
}

void
upf_cpu_usage_timer ()
{
    pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = 2;
    upf_alarm_msg_t *upf_alarm = 0;
    upf_main_t *gtm = &g_upf_main;

    vlib_node_main_t *nm;
    vlib_node_t *n;
    f64 *internal_node_vector_rates = 0;
    f64 cpu_usage;

    uword i, j;
    vlib_main_t **stat_vms = 0, *stat_vm;

    for (i = 0; i < vec_len (vlib_mains); i++)
    {
        stat_vm = vlib_mains[i];
        if (stat_vm)
            vec_add1 (stat_vms, stat_vm);
    }

    for (j = 0; j < vec_len (stat_vms); j++)
    {
        stat_vm = stat_vms[j];
        nm = &stat_vm->node_main;

        for (i = 0; i < vec_len (nm->nodes); i++)
        {
            n = nm->nodes[i];
            vlib_node_sync_stats (stat_vm, n);
        }

        vec_add1 (internal_node_vector_rates, vlib_internal_node_vector_rate (stat_vm));
    }
    //vlib_worker_thread_barrier_release (vm);


    for (j = 0; j < vec_len (stat_vms); j++)
    {
        if (vec_len (vlib_mains) > 1)
        {
            vlib_worker_thread_t *w = vlib_worker_threads + j;

            if (w->cpu_id > -1)
            {
                memcpy(g_upf_cpu_usage[j].thread_name, w->name, sizeof(g_upf_cpu_usage[j].thread_name));
                g_upf_cpu_usage[j].thread_id = j;
                g_upf_cpu_usage[j].cpu_id = w->cpu_id;
            }
        }

        g_upf_cpu_usage[j].active = 1;
        g_upf_cpu_usage[j].node_vector_rates = internal_node_vector_rates[j];
        g_upf_cpu_usage[j].cpu_usage = internal_node_vector_rates[j]*100.0/255.0;
    }
    vec_free (stat_vms);
    vec_free (internal_node_vector_rates);

    cpu_usage = upf_get_total_cpu_usage();

    if (g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER &&
        cpu_usage > g_upf_cpu_usage_threshold)
    {
        upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(f64), CLIB_CACHE_LINE_BYTES);
        memset (upf_alarm->data, 0, sizeof(f64));
        upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
        memcpy(upf_alarm->data, &cpu_usage, sizeof(f64));
        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }
        g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
    }

    //threshold - 1 ==>> The aim is to reduce jitter
    if (g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE &&
        cpu_usage < (g_upf_cpu_usage_threshold - 1))
    {
        upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(f64), CLIB_CACHE_LINE_BYTES);
        memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
        upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
        memcpy(upf_alarm->data, &cpu_usage, sizeof(f64));
        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }
        g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
    }

    upf_pfcp_server_timer_start (UPF_CPU_USAGE_TIMER, UPF_CPU_USAGE_TIMER, &timer);

}

void upf_cpu_usage_init()
{
    pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = 5;

    start_first_thread_timer(UPF_CPU_USAGE_TIMER, UPF_CPU_USAGE_TIMER, &timer);
}

void upf_alarm_init()
{
    BV (clib_bihash_init) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, "dl session check table",
               UPF_ALARM_DEFAULT_HASH_NUM_BUCKETS, UPF_ALARM_DEFAULT_HASH_MEMORY_SIZE);
    BV (clib_bihash_init) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, "ul ue ip check table",
               UPF_ALARM_DEFAULT_HASH_NUM_BUCKETS, UPF_ALARM_DEFAULT_HASH_MEMORY_SIZE);

    memset(&g_upf_alarm_state, 0, sizeof(g_upf_alarm_state));
    for (int i = 0; i < UPF_ALARM_MAX - UPF_ALARM_BASE; i++)
    {
        g_upf_alarm_state[i].alarm_type = i + UPF_ALARM_BASE + 1;
        upf_alarm_notify(g_upf_alarm_state[i].alarm_type, UPF_ALARM_TYPE_RECOVER, UPF_ALARM_LEVEL_GENERAL, NULL);
        g_upf_alarm_state[i].alarm_state = UPF_ALARM_TYPE_RECOVER;
    }

    memset(&g_upf_alarm_id_switch, 1, sizeof(g_upf_alarm_id_switch));
    memset(&g_upf_alarm_threshold, 0xFF, sizeof(g_upf_alarm_threshold));

    g_upf_alarm_threshold[UPF_ALARM_ASSCO_SETUP_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_ASSCO_UPDATE_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_ASSCO_RELEASE_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_PFD_MANAGEMENT_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_SESSION_EST_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_SESSION_MODIFY_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;
    g_upf_alarm_threshold[UPF_ALARM_SESSION_DELETE_SUCC_RATIO_LOW - UPF_ALARM_BASE] = 85;

    pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = 5;

    start_first_thread_timer(UPF_ALARM_TIMER, UPF_ALARM_TIMER, &timer);

    upf_run_independent_timer_reset(g_run_independent_timer * HOUR_TO_SECONDS);
}

// Add for dnn/s-nssai timeval by liupeng on 2021-11-16 below
void start_first_thread_timer(u8 type, u32 id, pfcp_time_t *timer)
{
    i64 interval;
    upf_per_pfcp_thread_t *per_pfcp = g_upf_main.per_pfcp_thread[0];

    if (!timer->period)
      return;

    /* period is relative duration in ticks */
    interval = timer->period * TW_CLOCKS_PER_SECOND -
               floor ((unix_time_now () - timer->base) * TW_CLOCKS_PER_SECOND);

    interval = clib_max (interval, 1); /* make sure interval is at least 1 */

    ASSERT (type <= 255);
    ASSERT ((id & 0xff000000) == 0);
    timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, (type << 24) | id, PFCP_URR_TIMER, interval);

    return;
}

void stop_first_thread_timer(u32 handle)
{
    upf_per_pfcp_thread_t *per_pfcp = g_upf_main.per_pfcp_thread[0];
    if (handle &&
        !tw_timer_handle_is_free_1t_3w_1024sl_ov (&per_pfcp->timer, handle))
      tw_timer_stop_1t_3w_1024sl_ov (&per_pfcp->timer, handle);
}
// Add for dnn/s-nssai timeval by liupeng on 2021-11-16 above

f64 upf_n4_msg_success_ratio_threshold = 80;

void upf_pfcp_msg_success_ratio_alarm(u32 total_cnt, u32 success_cnt, u32 success_ratio, u32 alarm_id)
{
    upf_alarm_msg_t *upf_alarm = 0;
    upf_main_t *gtm = &g_upf_main;
    n4_msg_alarm_t tmp_data;

    if (g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER &&
        success_ratio < g_upf_alarm_threshold[alarm_id - UPF_ALARM_BASE])
    {
        upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->alarm_id = alarm_id;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
        tmp_data.total_cnt = total_cnt;
		tmp_data.success_cnt = success_cnt;
		tmp_data.success_ratio = success_ratio;
		/*memcpy(tmp_data.msg_produce_name, msg_produce_name, strlen((const char *)msg_produce_name)+1);*/

		upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(n4_msg_alarm_t), CLIB_CACHE_LINE_BYTES);
		memset (upf_alarm->data, 0, sizeof(n4_msg_alarm_t));
		memcpy(upf_alarm->data, &tmp_data, sizeof(n4_msg_alarm_t));

        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }

        g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
    }

    if (g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE &&
        success_ratio > g_upf_alarm_threshold[alarm_id - UPF_ALARM_BASE])
    {
        upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->alarm_id = alarm_id;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
		tmp_data.total_cnt = total_cnt;
  	    tmp_data.success_cnt = success_cnt;
  	    tmp_data.success_ratio = success_ratio;
  	    /*memcpy(tmp_data.msg_produce_name, msg_produce_name, strlen((const char *)msg_produce_name)+1);*/

  	    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(n4_msg_alarm_t), CLIB_CACHE_LINE_BYTES);
  	    memset (upf_alarm->data, 0, sizeof(n4_msg_alarm_t));
  	    memcpy(upf_alarm->data, &tmp_data, sizeof(n4_msg_alarm_t));

        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }

        g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
    }
}

void upf_pfcp_msg_success_ratio_handle()
{
    upf_performance_measurement_t upf_stat;
	u32 msg_total = 0;
	u32 msg_success_cnt = 0;
	u32 msg_success_ratio = 0;

    iupf_get_total_statistics_data(&upf_stat);

    msg_total = upf_stat.n4_statistics.association_setup.req_times;
	msg_success_cnt = upf_stat.n4_statistics.association_setup.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_ASSCO_SETUP_SUCC_RATIO_LOW);
	}

	msg_total = upf_stat.n4_statistics.association_update.req_times;
	msg_success_cnt = upf_stat.n4_statistics.association_update.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_ASSCO_UPDATE_SUCC_RATIO_LOW);
	}


	msg_total = upf_stat.n4_statistics.association_release.req_times;
	msg_success_cnt = upf_stat.n4_statistics.association_release.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_ASSCO_RELEASE_SUCC_RATIO_LOW);
	}

	msg_total = upf_stat.n4_statistics.pfd_managenent.req_times;
	msg_success_cnt = upf_stat.n4_statistics.pfd_managenent.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_PFD_MANAGEMENT_SUCC_RATIO_LOW);
	}

	msg_total = upf_stat.n4_statistics.sess_estab.req_times;
	msg_success_cnt = upf_stat.n4_statistics.sess_estab.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_SESSION_EST_SUCC_RATIO_LOW);
	}

	msg_total = upf_stat.n4_statistics.sess_modify.req_times;
	msg_success_cnt = upf_stat.n4_statistics.sess_modify.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_SESSION_MODIFY_SUCC_RATIO_LOW);
	}

	msg_total = upf_stat.n4_statistics.sess_delete.req_times;
	msg_success_cnt = upf_stat.n4_statistics.sess_delete.handle_succ_times;
	if (msg_total > 0)
	{
	    msg_success_ratio = msg_success_cnt * 100 / msg_total;
		upf_pfcp_msg_success_ratio_alarm(msg_total, msg_success_cnt, msg_success_ratio, UPF_ALARM_SESSION_DELETE_SUCC_RATIO_LOW);
	}


}

void
upf_server_send_heartbeat (u32 node_idx)
{
  sx_server_main_t *sxsm = &sx_server_main;
  pfcp_heartbeat_request_t req;
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;

  if (pool_is_free_index (gtm->nodes, node_idx))
    return;
  n = pool_elt_at_index (gtm->nodes, node_idx);

  memset (&req, 0, sizeof (req));
  SET_BIT (req.grp.fields, HEARTBEAT_REQUEST_RECOVERY_TIME_STAMP);
  req.recovery_time_stamp = sxsm->start_time;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->send_heartbeat.req_times++;

  upf_pfcp_server_node_request_send (n, PFCP_HEARTBEAT_REQUEST, &req.grp);
}
void
upf_server_send_gtpu_echo_req (u32 peer_idx)
{
  upf_main_t *gtm = &g_upf_main;
  upf_peer_t *peer0 = NULL;
  clib_bihash_kv_8_8_t kv;

  /* neil.fan@20231018 fix the "running echo req/reply" cannot be stopped issue. */
  if (!g_upf_main.gtp_req_enable)
    {
      return;
    }

  kv.key = 0;
  kv.value = ~0;
  if (pool_is_free_index (gtm->peers, peer_idx))
  {
    upf_debug ("peer_idx:%u is invalid\n", peer_idx);
    return;
  }
  peer0 = pool_elt_at_index (gtm->peers, peer_idx);

  (peer0->seq)++;
  kv.key = peer0->seq;
  kv.value = peer_idx;
  clib_bihash_add_del_8_8 (&gtm->echo_req_by_seq, &kv, 1 /* is_add */);

  if (gtm->gtp_retry_switch == SWITCH_ON)
    peer0->n1 = 3;
  else
    peer0->n1 = 0;
  peer0->t1 = 1;

  peer0->retry_timer.base = unix_time_now ();
  peer0->retry_timer.period = peer0->t1;
  upf_pfcp_server_timer_start (GTPU_ECHO_REQ_T1, peer_idx,
                               &peer0->retry_timer);

  //neil.fan@20211115 modify begin, sending the msg immediately.
  upf_debug ("send echo req idx:%u seq:%u remote ip:%U\n", peer_idx,
    peer0->seq, format_ip46_address, &peer0->ohc_ip, IP46_TYPE_ANY);

  upf_gtp_echo_req (peer0, peer_idx);

  if (peer0->timer.period != gtm->gtpu_echo_req_period)
    {
      peer0->timer.period = gtm->gtpu_echo_req_period;
      peer0->timer.base += peer0->timer.period;
    }
  else
    {
      peer0->timer.period = gtm->gtpu_echo_req_period;
      peer0->timer.base = unix_time_now ();
    }
  upf_pfcp_server_timer_start (GTPU_ECHO_REQ_TIMER, peer_idx, &peer0->timer);
  //neil.fan@20211115 modify end
}

u32 upf_gtp_peer_ip_check (ip46_address_t *ip)
{
    //u8 action[64] = "nb_ip";
    upf_whitelist_t *upf_whitelist = NULL;

    if (!upf_ip_whitelist_is_empty((u8 *)"nb_ip"))
    {
        upf_whitelist = upf_lookup_upf_ip_whitelist(ip, NULL, NULL, (u8 *)"nb_ip");
        if (!upf_whitelist)
        {
            upf_err ("remote ip: %U is not in whitelist", format_ip46_address, ip, IP46_TYPE_IP4);
            return 1;
        }
    }

    return 0;
}

upf_pfcp_endpoint_t *upf_pfcp_endpoint_get_by_asso (upf_node_assoc_t *n)
{
    upf_main_t *gtm = &g_upf_main;
    ip46_address_fib_t key = {0};
    key.addr = n->lcl_addr;
    key.fib_index = n->fib_index;

    uword *p = hash_get_mem (gtm->pfcp_endpoint_index, &key);
    if (!p)
    {
        upf_err ("pfcp endpoint not found for %u %U", key.fib_index,
                 format_ip46_address, &key.addr);
        return NULL;
    }

    if (pool_is_free_index(gtm->pfcp_endpoints, p[0]))
        return NULL;

    return pool_elt_at_index (gtm->pfcp_endpoints, p[0]);
}

u32 upf_set_node_id_by_asso_idx (pfcp_node_id_t *node_id, upf_node_assoc_t *n )
{
    upf_pfcp_endpoint_t *ep = upf_pfcp_endpoint_get_by_asso (n);
    if (!ep)
      return 1;

    if (ip46_address_is_ip4 (&ep->key.addr))
      {
        node_id->type = NID_IPv4;
        ip46_address_set_ip4 (&node_id->ip, &ep->key.addr.ip4);
      }
    else
      {
        node_id->type = NID_IPv6;
        ip46_address_set_ip6 (&node_id->ip, &ep->key.addr.ip6);
      }
    return 0;
}

void upf_server_send_node_report_req (u64 type, u32 asso_idx, upf_peer_t *peer0)
{
    pfcp_node_report_request_t req = {0};
    upf_main_t *gtm = &g_upf_main;

    upf_debug ("node index: %d type:%d remote ip: %U ", asso_idx, type, format_ip46_address, &peer0->ohc_ip, IP46_TYPE_IP4);

    if (upf_gtp_peer_ip_check(&peer0->ohc_ip))
        return;

    if (pool_is_free_index (gtm->nodes, asso_idx))
        return;
    upf_node_assoc_t *n = pool_elt_at_index (gtm->nodes, asso_idx);

    if (upf_set_node_id_by_asso_idx (&req.request.node_id, n))
        return;
    SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_ID);

    SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_REPORT_TYPE);

    if (type & (NRT_USER_PLANE_PATH_FAILURE_REPORT | NRT_USER_PLANE_PATH_RECOVERY_REPORT | NRT_GTP_U_PATH_QOS_REPORT))
    {
        pfcp_remote_gtp_u_peer_t remote_gtp_u_peer = {0};
        remote_gtp_u_peer.destination_interface = ~0;
        clib_memcpy_fast (&remote_gtp_u_peer.ip, &peer0->ohc_ip, sizeof (peer0->ohc_ip));

        if (type & NRT_USER_PLANE_PATH_FAILURE_REPORT)
        {
          req.node_report_type.flags |= NRT_USER_PLANE_PATH_FAILURE_REPORT;
          SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_USER_PLANE_PATH_FAILURE_REPORT);
          SET_BIT (req.user_plane_path_failure_report.grp.fields, USER_PLANE_PATH_FAILURE_REPORT_REMOTE_GTP_U_PEER);
          vec_add1 (req.user_plane_path_failure_report.remote_gtp_u_peer, remote_gtp_u_peer);
        }
        else if (type & NRT_USER_PLANE_PATH_RECOVERY_REPORT)
        {
          req.node_report_type.flags |= NRT_USER_PLANE_PATH_RECOVERY_REPORT;
          SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_USER_PLANE_PATH_RECOVERY_REPORT);
          SET_BIT (req.user_plane_path_recovery_report.grp.fields, USER_PLANE_PATH_FAILURE_REPORT_REMOTE_GTP_U_PEER);
          vec_add1 (req.user_plane_path_recovery_report.remote_gtp_u_peer, remote_gtp_u_peer);
        }

        if (type & NRT_GTP_U_PATH_QOS_REPORT)
        {
            req.node_report_type.flags |= NRT_GTP_U_PATH_QOS_REPORT;
            SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_GTPU_PATH_QOS_REPORT);

            pfcp_gtpu_path_qos_report_t *q;
            APPEND_NEW_MEMBER (req.gtpu_path_qos_report, q);
            SET_BIT (q->grp.fields, GTPU_PATH_QOS_REPORT_REMOTE_GTPU_PEER);
            clib_memcpy_fast (&q->remote_gtpu_peer, &remote_gtp_u_peer, sizeof (remote_gtp_u_peer));

            SET_BIT (q->grp.fields, GTPU_PATH_QOS_REPORT_QOS_REPORT_TRIGGER);
            q->qos_report_trigger.flags |= IE_QOS_REPORT_TRIGGER_PER;

            SET_BIT (q->grp.fields, GTPU_PATH_QOS_REPORT_TIME_STAMP);
            q->time_stamp = (u32)unix_time_now();

            SET_BIT (q->grp.fields, GTPU_PATH_QOS_REPORT_START_TIME);
            q->start_time = (u32)peer0->qos_monitor_t0;

            SET_BIT (q->grp.fields, GTPU_PATH_QOS_REPORT_QOS_INFO);
            pfcp_qos_info_t *qos_info;
            APPEND_NEW_MEMBER (q->qos_info, qos_info);

            SET_BIT (qos_info->grp.fields, QOS_INFO_AVERAGE_PACKET_DELAY);
            qos_info->average_packet_delay = peer0->qos_monitor_delay;
        }
    }

    upf_pfcp_server_node_request_send (n, PFCP_NODE_REPORT_REQUEST, &req.grp);
}

void
upf_server_send_association_update_pfcp_thread (sx_asso_update_para_t *para)
{
  // sx_server_main_t *sxsm = &sx_server_main;
  pfcp_association_update_request_t req;
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;
  ip46_address_fib_t key;
  upf_pfcp_endpoint_t *ep;
  uword *p;
  // sx_asso_update_para_t *para = (sx_asso_update_para_t *)arg;

  upf_info ("node index: %d release: %d", para->node_index, para->release);
  if (pool_is_free_index (gtm->nodes, para->node_index))
    return;
  n = pool_elt_at_index (gtm->nodes, para->node_index);

  clib_memset (&req, 0, sizeof (req));

  SET_BIT (req.grp.fields, ASSOCIATION_SETUP_RESPONSE_NODE_ID);

  key.addr = n->lcl_addr;
  key.fib_index = n->fib_index;

  p = hash_get_mem (gtm->pfcp_endpoint_index, &key);
  if (!p)
    {
      upf_err ("pfcp endpoint not found for %u %U", key.fib_index,
               format_ip46_address, &key.addr);
      return;
    }

  CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
  ep = pool_elt_at_index (gtm->pfcp_endpoints, p[0]);

  if (ip46_address_is_ip4 (&ep->key.addr))
    {
      req.request.node_id.type = NID_IPv4;
      ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
    }
  else
    {
      req.request.node_id.type = NID_IPv6;
      ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
    }

  SET_BIT (req.grp.fields,
           ASSOCIATION_UPDATE_REQUEST_PFCP_ASSOCIATION_RELEASE_REQUEST);
  req.pfcp_association_release_request.flags |=
      F_PFCP_ASSOCIATION_RELEASE_REQUEST_SARR;

  if (!para->release)
    {
      SET_BIT (req.grp.fields,
               ASSOCIATION_UPDATE_REQUEST_GRACEFUL_RELEASE_PERIOD);
      req.graceful_release_period = para->graceful_release_period;
    }

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_update.req_times++;

  upf_pfcp_server_node_request_send (n, PFCP_ASSOCIATION_UPDATE_REQUEST,
                                     &req.grp);
  clib_mem_free (para);
}
void
upf_server_send_association_up_features_update_pfcp_thread (void *arg)
{
  // sx_server_main_t *sxsm = &sx_server_main;
  pfcp_association_update_request_t req;
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;
  ip46_address_fib_t key;
  upf_pfcp_endpoint_t *ep;
  uword *p;
  u64 node_idx = (u64)arg;

  upf_info ("node index: %d", node_idx);
  if (pool_is_free_index (gtm->nodes, node_idx))
    return;
  n = pool_elt_at_index (gtm->nodes, node_idx);

  clib_memset (&req, 0, sizeof (req));

  SET_BIT (req.grp.fields, ASSOCIATION_SETUP_RESPONSE_NODE_ID);
  SET_BIT (req.grp.fields, ASSOCIATION_UPDATE_REQUEST_UP_FUNCTION_FEATURES);

  key.addr = n->lcl_addr;
  key.fib_index = n->fib_index;

  p = hash_get_mem (gtm->pfcp_endpoint_index, &key);
  if (!p)
    {
      upf_err ("pfcp endpoint not found for %u %U", key.fib_index,
               format_ip46_address, &key.addr);
      return;
    }

  CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
  ep = pool_elt_at_index (gtm->pfcp_endpoints, p[0]);

  if (ip46_address_is_ip4 (&ep->key.addr))
    {
      req.request.node_id.type = NID_IPv4;
      ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
    }
  else
    {
      req.request.node_id.type = NID_IPv6;
      ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
    }

  req.up_function_features = gtm->upf_features;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_update.req_times++;

  upf_pfcp_server_node_request_send (n, PFCP_ASSOCIATION_UPDATE_REQUEST,
                                     &req.grp);
}
static clib_error_t *
upf_asso_request_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;
  sx_server_main_t *sxsm = &sx_server_main;
  pfcp_association_setup_request_t req;
  ip46_address_t cp_ip, dp_ip;
  upf_node_assoc_t n;
  u32 fib_index = 0;
  sx_msg_t *msg;

  clib_memset (&req, 0, sizeof (pfcp_association_setup_request_t));
  clib_memset (&cp_ip, 0, sizeof (ip46_address_t));
  clib_memset (&dp_ip, 0, sizeof (ip46_address_t));
  clib_memset (&n, 0, sizeof (upf_node_assoc_t));

  if (unformat_user (main_input, unformat_line_input, line_input))
    {
	  /* begin modified by liuyu UPF_CMD_MOD 200-08-22 */
      while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
          if (unformat (line_input, "cp_ipaddr %U", unformat_ip46_address, &cp_ip,
                        IP46_TYPE_ANY))
            n.rmt_addr = cp_ip;
          else if (unformat (line_input, "dp_ipaddr %U", unformat_ip46_address,
                             &dp_ip, IP46_TYPE_ANY))
            n.lcl_addr = dp_ip;
          else if (unformat (line_input, "vrf_value %d", &fib_index))
            n.fib_index = fib_index;
          else
            {
              error = unformat_parse_error (line_input);
              unformat_free (line_input);
              goto done;
            }
        }
	  /* end modified by liuyu UPF_CMD_MOD 200-08-22 */

      unformat_free (line_input);
    }

  memset (&req, 0, sizeof (req));
  SET_BIT (req.grp.fields, ASSOCIATION_SETUP_REQUEST_NODE_ID);
  if (ip46_address_is_ip4 (&dp_ip))
    {
      req.request.node_id.type = NID_IPv4;
      ip46_address_set_ip4 (&req.request.node_id.ip, &dp_ip.ip4);
    }
  else
    {
      req.request.node_id.type = NID_IPv6;
      ip46_address_set_ip6 (&req.request.node_id.ip, &dp_ip.ip6);
    }
  SET_BIT (req.grp.fields, ASSOCIATION_SETUP_REQUEST_RECOVERY_TIME_STAMP);
  req.recovery_time_stamp = sxsm->start_time;
  SET_BIT (req.grp.fields, ASSOCIATION_SETUP_REQUEST_UP_FUNCTION_FEATURES);
  req.up_function_features = gtm->upf_features;
  SET_BIT (req.grp.fields,
           ASSOCIATION_SETUP_REQUEST_USER_PLANE_IP_RESOURCE_INFORMATION);
  upf_build_user_plane_ip_resource_information (
      &req.user_plane_ip_resource_information);

  // upf_pfcp_server_node_request_send (&n, PFCP_ASSOCIATION_SETUP_REQUEST,
  // &req.grp);
  pool_get_aligned (sxsm->msg_pool, msg, CLIB_CACHE_LINE_BYTES);
  sx_node_msg_encode (&n, PFCP_ASSOCIATION_SETUP_REQUEST, &req.grp, msg);
  upf_send_pfcp_data (msg);

done:
  return error;
}
/*  begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_asso_request_command, static) = {
    .path = "upf asso req",
    .short_help = "upf asso req [cp_ipaddr <x.x.x.x>] [dp_ipaddr "
                  "<x.x.x.x>] vrf_value <table_idx>",
    .function = upf_asso_request_command_fn,
};
/* *INDENT-ON* */
/* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
static clib_error_t *
upf_asso_update_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                            vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;
  u32 node_index = ~0;
  u32 unit = 0, value = 0, has_node_index = 0, has_graceful_unit = 0,
      has_graceful_value = 0;
  upf_node_assoc_t *n = NULL;
  sx_asso_update_para_t para;

  clib_memset (&para, 0, sizeof (sx_asso_update_para_t));
  if (unformat_user (main_input, unformat_line_input, line_input))
    {
      while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
          /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
          if (unformat (line_input, "node_idx %ld", &node_index))
            has_node_index = 1;
          else if (unformat (line_input, "grace_rel_unit"))
            has_graceful_unit = 1;
          else if (unformat (line_input, "grace_rel_val %d", &value))
            has_graceful_value = 1;
          else if (unformat (line_input, "twoSeconds"))
            unit = 0;
          else if (unformat (line_input, "oneMinutes"))
            unit = 1;
          else if (unformat (line_input, "tenMinutes"))
            unit = 2;
          else if (unformat (line_input, "oneHour"))
            unit = 3;
          else if (unformat (line_input, "tenHours"))
            unit = 4;
          else if (unformat (line_input, "limitless"))
            unit = 7;
          else
            {
              error = unformat_parse_error (line_input);
              unformat_free (line_input);
              goto done;
            }
          /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
        }

      unformat_free (line_input);
    }

  if (has_graceful_unit ^ has_graceful_value)
    {
	  /* modified by liuyu UPF_CMD_MOD 2022-08-22 */
      vlib_cli_output (vm, "PFCP asso graceful param err!!");
      goto done;
    }
  else if (has_graceful_unit)
    {
      para.graceful_release_period.unit = unit;
      para.graceful_release_period.value = value;
    }
  else
    para.release = 1;

  if (has_node_index)
    {
      if (pool_is_free_index (gtm->nodes, node_index))
        {
		  /* modified by liuyu UPF_CMD_MOD 2022-08-22 */
          vlib_cli_output (vm, "node idx is not found in all nodes!!");
          goto done;
        }
      n = pool_elt_at_index (gtm->nodes, node_index);
      if (n)
        {
          sx_asso_update_para_t *para_p = clib_mem_alloc_aligned_no_fail (
              sizeof (sx_asso_update_para_t), CLIB_CACHE_LINE_BYTES);
          clib_memcpy (para_p, &para, sizeof (sx_asso_update_para_t));
          para_p->node_index = node_index;
          pfcp_send_rpc_to_thread (
              0, upf_server_send_association_update_pfcp_thread,
              (sx_asso_update_para_t *)para_p);
        }
      else
        {
		  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
          error =
              clib_error_return (0, "node idx is not found in all nodes!!");
          /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
		}
    }
  else
    {
      /* *INDENT-OFF* */
      pool_foreach (
          n, gtm->nodes, ({
            sx_asso_update_para_t *para_p = clib_mem_alloc_aligned_no_fail (
                sizeof (sx_asso_update_para_t), CLIB_CACHE_LINE_BYTES);
            clib_memcpy (para_p, &para, sizeof (sx_asso_update_para_t));
            para_p->node_index = n - gtm->nodes;
            pfcp_send_rpc_to_thread (
                0, upf_server_send_association_update_pfcp_thread,
                (void *)para_p);
          }));
      /* *INDENT-ON* */
    }

done:
  return error;
}
/* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_asso_update_command, static) = {
    .path = "upf asso update",
    .short_help = "upf asso update [node_idx X] "
                  "[grace_rel_unit twoSeconds | oneMinutes | tenMinutes | "
                  "oneHour | tenHours | limitless grace_rel_val X]",
    .function = upf_asso_update_command_fn,
};
/* *INDENT-ON* */
/* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
static clib_error_t *
upf_status_set_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                            vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;
  upf_node_assoc_t *n = NULL;
  sx_asso_update_para_t para;

  clib_memset (&para, 0, sizeof (sx_asso_update_para_t));
  if (unformat_user (main_input, unformat_line_input, line_input))
    {
      while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
          if (unformat (line_input, "up"))
            gtm->upf_status = UPF_STATUS_UP;
          else if (unformat (line_input, "down"))
            gtm->upf_status = UPF_STATUS_DOWN;
          else
            {
              error = unformat_parse_error (line_input);
              unformat_free (line_input);
              goto done;
            }
        }

      unformat_free (line_input);
    }

  if (gtm->upf_status == UPF_STATUS_DOWN)
    {
        para.release = 1;
        pool_foreach (
            n, gtm->nodes, ({
              sx_asso_update_para_t *para_p = clib_mem_alloc_aligned_no_fail (
                  sizeof (sx_asso_update_para_t), CLIB_CACHE_LINE_BYTES);
              clib_memcpy (para_p, &para, sizeof (sx_asso_update_para_t));
              para_p->node_index = n - gtm->nodes;
              pfcp_send_rpc_to_thread (
                  0, upf_server_send_association_update_pfcp_thread,
                  (void *)para_p);
            }));
    }

done:
  return error;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_status_set_command, static) = {
    .path = "upf status",
    .short_help = "upf status [up|down] ",
    .function = upf_status_set_command_fn,
};
/* *INDENT-ON* */

static clib_error_t *
upf_status_show_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                            vlib_cli_command_t *cmd)
{
    upf_main_t *gtm = &g_upf_main;
    clib_error_t *error = NULL;

    vlib_cli_output (vm, "upf status is %s", gtm->upf_status ? "UP" : "DOWN");

    return error;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_status_show_command, static) = {
    .path = "show upf status",
    .short_help = "show upf status",
    .function = upf_status_show_command_fn,
};
/* *INDENT-ON* */

static void
upf_buffering_deliver_packets (vlib_main_t *vm, u32 to_node_index, u32 *bi,
                               u32 len)
{
  vlib_frame_t *frame;
  u32 *to_next;
  u32 i, j;

  i = 0;
  while (len >= VLIB_FRAME_SIZE)
    {
      frame = vlib_get_frame_to_node (vm, to_node_index);
      to_next = vlib_frame_vector_args (frame);

      clib_memcpy_fast (to_next, bi + i, sizeof (u32) * VLIB_FRAME_SIZE);
      frame->n_vectors = VLIB_FRAME_SIZE;

      for (j = 0; j < VLIB_FRAME_SIZE; j++)
        {
          upf_debug ("send bulk buffer %u\n", (bi + i)[j]);
        }

      vlib_put_frame_to_node (vm, to_node_index, frame);

      len -= VLIB_FRAME_SIZE;
      i += VLIB_FRAME_SIZE;
    }

  if (len)
    {
      frame = vlib_get_frame_to_node (vm, to_node_index);
      to_next = vlib_frame_vector_args (frame);

      clib_memcpy_fast (to_next, bi + i, sizeof (u32) * len);
      frame->n_vectors = len;

      for (j = 0; j < len; j++)
        {
          upf_debug ("send scalar buffer %u\n", (bi + i)[j]);
        }

      vlib_put_frame_to_node (vm, to_node_index, frame);

      vec_reset_length (bi);
    }
  vec_free (bi);
}

static void
upf_buffering_send_packets (vlib_main_t *vm, upf_session_t *sess,
                            upf_far_t *far)
{
  u32 to_node_index;

  to_node_index = sess->pdn_type > 1 ? upf_process_ip4_node.index
                                   : upf_process_ip6_node.index;
  if (far->downlink_buf_n > 0)
  {
    upf_buffering_deliver_packets (vm, to_node_index, far->buffer_bi[UPF_DL], far->downlink_buf_n);
    far->buffer_bi[UPF_DL] = NULL;
    if (g_upf_main.far_buffering_n >= far->downlink_buf_n)
    {
      clib_atomic_fetch_sub (&g_upf_main.far_buffering_n, far->downlink_buf_n);
  	}
   	else
   	{
      UPF_STATISTICS_ADD(UPF_ALL_BUFFERED_SIGNAL_FAR_BUFFERED_INCONSISTENT);
	  upf_err("buffer count inconsistent, far_buffering_n %u downlink_buf_n %u\n",
               g_upf_main.far_buffering_n, far->downlink_buf_n);
      clib_atomic_fetch_and (&g_upf_main.far_buffering_n, 0);
   	}
      g_upf_main.far_buffering_n = (g_upf_main.far_buffering_n < 0) ? 0 :g_upf_main.far_buffering_n;
  	clib_atomic_fetch_and (&far->downlink_buf_n, 0);
  }

  if (far->uplink_buf_n > 0)
  {
  	upf_buffering_deliver_packets (vm, to_node_index, far->buffer_bi[UPF_UL], far->uplink_buf_n);
  	far->buffer_bi[UPF_UL] = NULL;
  	if (g_upf_main.far_buffering_n >= far->uplink_buf_n)
  	{
      clib_atomic_fetch_sub (&g_upf_main.far_buffering_n, far->uplink_buf_n);
  	}
  	else
  	{
  	  UPF_STATISTICS_ADD(UPF_ALL_BUFFERED_SIGNAL_FAR_BUFFERED_INCONSISTENT);
  	  upf_err("buffer count inconsistent, far_buffering_n %u uplink_buf_n %u\n",
           g_upf_main.far_buffering_n, far->uplink_buf_n);
  	  clib_atomic_fetch_and (&g_upf_main.far_buffering_n, 0);
  	}
      g_upf_main.far_buffering_n = (g_upf_main.far_buffering_n < 0) ? 0 :g_upf_main.far_buffering_n;
  	clib_atomic_fetch_and (&far->uplink_buf_n, 0);
  }
}

void
upf_pfcp_msg_request (sx_msg_t *msg)
{
  vlib_main_t *vm = vlib_get_main ();
  vlib_process_signal_event_mt (vm, sx_api_process_node.index, EVENT_RX,
                                (uword)msg);
}

int
upf_downlink_forward_request (upf_session_t *sess, u32 far_id)
{
  sx_server_main_t *sx = &sx_server_main;
  vlib_main_t *vm = sx->vlib_main;
  upf_downlink_buf_msg_t *msg;

  msg = clib_mem_alloc_aligned_no_fail (sizeof (*msg), CLIB_CACHE_LINE_BYTES);
  memset (msg, 0, sizeof (*msg));

  msg->session_index = sess->up_seid;
  msg->far_id = far_id;

  vlib_process_signal_event_mt (vm, sx_api_process_node.index, EVENT_FORWARD,
                                (uword)msg);

  return 0;
}

static void
upf_buffering_process (vlib_main_t *vm, uword *event_data)
{
  upf_session_t *sess = NULL;
  upf_far_t *far = NULL;
  struct rules *active;

  if (vec_len (event_data) == 0)
    {
      return;
    }

  for (int i = 0; i < vec_len (event_data); i++)
    {
      upf_downlink_buf_msg_t *msg = (upf_downlink_buf_msg_t *)event_data[i];

      sess = upf_session_lookup (msg->session_index);
      if (sess == NULL)
        {
          clib_mem_free (msg);
          continue;
        }
      upf_debug("send buffering pkt and  related ueip: %U", format_ip46_address, &sess->ue_address, IP46_TYPE_ANY);
      active = upf_get_rules (sess, SX_ACTIVE);
      far = upf_get_far_by_id (active, msg->far_id);
      if (far == NULL)
        {
          clib_mem_free (msg);
          continue;
        }
      clib_spinlock_lock (&sess->lock);
      upf_buffering_send_packets (vm, sess, far);
      clib_spinlock_unlock (&sess->lock);
      clib_mem_free (msg);
    }

  return;
}

static uword
sx_process (vlib_main_t *vm, vlib_node_runtime_t *rt, vlib_frame_t *f)
{
  sx_server_main_t *sxsm = &sx_server_main;
  uword event_type, *event_data = 0;
  upf_main_t *gtm = &g_upf_main;

  while (1)
    {
      (void)vlib_process_wait_for_event_or_clock (vm, 10e-3);
      event_type = vlib_process_get_events (vm, &event_data);

      sxsm->now = unix_time_now ();

      switch (event_type)
        {
        case ~0: /* timeout */
          break;

        case EVENT_RX:
          {
            for (int i = 0; i < vec_len (event_data); i++)
              {
                sx_msg_t *msg = (sx_msg_t *)event_data[i];

                upf_pfcp_server_msg_rx (msg);
              }
            break;
          }

        case EVENT_TX:
          {
            for (int i = 0; i < vec_len (event_data); i++)
              {
                sx_msg_t *tx = (sx_msg_t *)event_data[i];

                if (!pool_is_free_index (gtm->nodes, tx->node))
                  {
                    sx_msg_t *msg;

                    clib_spinlock_lock (&sxsm->lock);
                    pool_get_aligned (sxsm->msg_pool, msg,
                                      CLIB_CACHE_LINE_BYTES);
                    clib_spinlock_unlock (&sxsm->lock);

                    *msg = *tx;

                    upf_pfcp_server_request_send (msg);
                  }
                else
                  {
                    vec_free (tx->data);
                    vec_free (tx->dnn);
                  }

                clib_mem_free (tx);
              }
            break;
          }

        case EVENT_URR:
          {
            for (int i = 0; i < vec_len (event_data); i++)
              {
                uword si = (uword)event_data[i];
                upf_session_t *sx;

                if (PREDICT_TRUE(!pool_is_free_index(gtm->sessions, si)))
                {
                    sx = pool_elt_at_index (gtm->sessions, si);
                    // upf_debug ("URR Event on Session Idx: %wd, %p\n", si,
                    // sx);
                    upf_pfcp_session_report_usage (sx, sxsm->now, UPF_INVALID_PDR);
                }
              }
            break;
          }

        case EVENT_FORWARD:
          {
            upf_buffering_process (vm, event_data);
            break;
          }

        default:
          upf_debug ("event %ld, %p. ", event_type, event_data[0]);
          break;
        }

      if (event_data)
        {
          _vec_len (event_data) = 0;
          vec_free (event_data);
        }

      for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
          vlib_node_set_interrupt_pending (
              vlib_mains[i + gtm->first_pfcp_thread_index],
              pfcp_timer_node.index);
        }
    }

  return (0);
}

/*add for dns sniffer begin by lixiao*/

#define time_after_eq(a,b) ((long)((a)-(b)) >= 0)

void dns_sniffer_ageing_timer()
{
    u32 i=0;
    dns_rule_rr_t *dns_sniffer_rule;
    ip46_address_t ip = {0};
    //uword *p=NULL;

    /*for(i=0;i<RULE_CNT;i++)
    {
        if(g_rule[i].acHost[0] && time_after_eq(rte_get_timer_cycles(),g_rule[i].ulExpire))
        {
            //rte_hash_del_key(ip_h,(void *)(&g_rule[i].uip));
            //g_rule[i].uip = 0;
            memset(&g_rule[i],0,sizeof(g_rule[i]));
            rte_hash_del_key(url_h,(void *)(&g_rule[i].acHost));
            upf_debug ("dns sniffer g_rule[%d].acHost=%s Expired ", i,g_rule[i].acHost);
        }
    }*/

    pool_foreach (dns_sniffer_rule, g_dns_sniffer_rule, ({
      if ((dns_sniffer_rule->dns_rule_key.acHost[0]) && (dns_sniffer_rule->uip[0]) && (time_after_eq(rte_get_timer_cycles(),dns_sniffer_rule->ulExpire)))
      {
          //p = hash_get_mem (g_dns_sniffer_rule, &key);
          //memset(dns_sniffer_rule,0,sizeof(*dns_sniffer_rule));
          //upf_graylist = pool_elt_at_index (g_dns_sniffer_rule, p[0]);
          for(i=0;i<MAX_IP_CNT;i++)
          {
            if(dns_sniffer_rule->uip[i])
            {
                ip.ip4.data_u32=dns_sniffer_rule->uip[i];
            }
          }
          //upf_whitelist_upf_ip_add_del(&ip,F_WHITELIST_IP,NULL,NULL,NULL,0,NULL,0);
          hash_unset_mem_free (&g_dns_sniffer_rule_hash, &dns_sniffer_rule->dns_rule_key);
          memset(dns_sniffer_rule,0,sizeof(*dns_sniffer_rule));
          pool_put (g_dns_sniffer_rule, dns_sniffer_rule);
          upf_debug ("dns sniffer dns_sniffer_rule->dns_rule_key.acHost=%s Expired ",dns_sniffer_rule->dns_rule_key.acHost);
      }
    }));
}
/*add for dns sniffer end by lixiao*/

// Add for dynmic_mac time refresh by liupeng on 2022-05-09 below
void upf_dynmic_mac_state_refresh(vlib_main_t *vm)
{
    upf_main_t *gtm = &g_upf_main;

    f64 current_time = (u32)vlib_time_now (vm);
    // refresh dynmac state

    upf_l2_key_t *tmp_l2_key = NULL;
    upf_l2_forw_t *tmp_l2_forw = NULL;

    hash_foreach_mem (tmp_l2_key, tmp_l2_forw, gtm->hash_eth_unicast_by_mac,
    ({
        if ((tmp_l2_forw->port_index == UPF_L2_DST_MAC_LEARN) && (tmp_l2_forw->type == L2_FORW_DYNAMIC))
        {
            if ((tmp_l2_forw->available == 0)
                 && ((current_time - tmp_l2_forw->timestamp) > gtm->dynmic_mac_refresh_timeval))
            {
                tmp_l2_forw->available = 1;
                tmp_l2_forw->bcast_pkts = gtm->allow_bcast_pkts;
                tmp_l2_forw->timestamp = current_time;
            }
        }
    }));

    gtm->dynmic_mac_refresh_timer.base = unix_time_now ();
    gtm->dynmic_mac_refresh_timer.period = gtm->dynmic_mac_refresh_timeval;

    start_first_thread_timer(PFCP_DYNMIC_MAC_REFRESH_TIMER, PFCP_DYNMIC_MAC_REFRESH_TIMER, &gtm->dynmic_mac_refresh_timer);
}

void upf_dynmic_mac_inactive_delete(vlib_main_t *vm)
{
    upf_main_t *gtm = &g_upf_main;
    u32 current_time = (u32)vlib_time_now (vm);
    upf_l2_key_t *k = NULL;
    upf_l2_forw_t *v = NULL;
    upf_l2_key_t **keys_to_free = NULL;

    upf_broadcast_key_t *upf_broadcat_key_k = NULL;
    upf_broadcast_t *upf_broadcat_key_v = NULL;
    upf_broadcast_key_t **upf_broadcast_keys_to_free = NULL;

    /*delete broadcast table*/
    hash_foreach_mem (upf_broadcat_key_k,upf_broadcat_key_v, gtm->hash_eth_broadcast_by_key,
    ({
        vec_add1 (upf_broadcast_keys_to_free, upf_broadcat_key_k);
    }));

    for (int index = 0; index < vec_len (upf_broadcast_keys_to_free); index++)
      upf_broadcast_hash_add_del(&gtm->hash_eth_broadcast_by_key, upf_broadcast_keys_to_free[index], DELETE);
    
    vec_free (upf_broadcast_keys_to_free);

    hash_foreach_mem (k, v, gtm->hash_eth_unicast_by_mac,
    ({
        if ((v->type == L2_FORW_DYNAMIC)
            && (current_time > v->timestamp + gtm->dynmic_mac_delete_timeval))
        {
            vec_add1 (keys_to_free, k);
        }
    }));

    for (int i = 0; i < vec_len (keys_to_free); i++)
      upf_l2_unicast_hash_add_del(&gtm->hash_eth_unicast_by_mac, keys_to_free[i], DELETE);
    vec_free (keys_to_free);

    gtm->dynmic_mac_delete_timer.base = unix_time_now ();
    gtm->dynmic_mac_delete_timer.period = gtm->dynmic_mac_delete_timeval;
    start_first_thread_timer(PFCP_DYNMIC_MAC_DELETE_TIMER, PFCP_DYNMIC_MAC_DELETE_TIMER, &gtm->dynmic_mac_delete_timer);
}

//begin liukang add for upf gre/ipsec tunnel detection 2022/06/22

void
upf_server_send_node_tunnel_report_req (u32 type, u32 node_idx,char *tunnel1)
{
  // sx_server_main_t *sxsm = &sx_server_main;
  pfcp_node_report_request_t req;
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;
  ip46_address_fib_t key;
  upf_pfcp_endpoint_t *ep;
  upf_dnn_tunnel_t *dnn_tunnel;
  char dnn[64] = {0};

  uword *p;

  if (pool_is_free_index (gtm->nodes, node_idx))
    return;
  n = pool_elt_at_index (gtm->nodes, node_idx);

  memset (&req, 0, sizeof (req));

  SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_ID);

  key.addr = n->lcl_addr;
  key.fib_index = n->fib_index;

  p = hash_get_mem (gtm->pfcp_endpoint_index, &key);
  if (!p)
    {
      upf_err ("pfcp endpoint not found for %u %U", key.fib_index,
               format_ip46_address, &key.addr);
      return;
    }

  CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
  ep = pool_elt_at_index (gtm->pfcp_endpoints, p[0]);

  if (ip46_address_is_ip4 (&ep->key.addr))
    {
      req.request.node_id.type = NID_IPv4;
      ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
    }
  else
    {
      req.request.node_id.type = NID_IPv6;
      ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
    }

  SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_REPORT_TYPE);

  req.vendor_specific_node_report.vendor_id = 33333;

  vec_foreach (dnn_tunnel, g_upf_main.dnn_tunnel)
  {

      if(strcmp(dnn_tunnel->tunnel,tunnel1) == 0)
      {
          strcpy(dnn,dnn_tunnel->dnn);
      }
  }

  if (PATH_FAILURE == type)
    {
      req.node_report_type.flags |= NRT_VENDOR_SPECIFIC_REPORT;
      SET_BIT (req.grp.fields,
               NODE_VENDOR_SPECIFIC_NODE_REPORT);

      //req.vendor_specific_node_report.specific_node_report_type.flags |= VENDOR_SPECIFIC_TUNNEL_FAILURE_REPORT;
      //SET_BIT (req.vendor_specific_node_report.grp.fields,
     //          NODE_VENDOR_SPECIFIC_TUNNEL_FAILURE_REPORT);

      //SET_BIT (req.vendor_specific_node_report.dnn_tunnel_failure_report.grp.fields,
      //         USER_VENDOR_SPECIFIC_DNN);

      req.vendor_specific_node_report.specific_node_report_type = VENDOR_SPECIFIC_TUNNEL_FAILURE_REPORT;
      req.vendor_specific_node_report.apn_dnn =
        format (req.vendor_specific_node_report.apn_dnn, "%s", dnn);
    }
  else if (PATH_RECOVERY == type)
    {
      req.node_report_type.flags |= NRT_VENDOR_SPECIFIC_REPORT;
      SET_BIT (req.grp.fields,
               NODE_VENDOR_SPECIFIC_NODE_REPORT);

      //req.vendor_specific_node_report.specific_node_report_type.flags |= VENDOR_SPECIFIC_TUNNEL_FAILURE_REPORT;
      //SET_BIT (req.vendor_specific_node_report.grp.fields,
     //          NODE_VENDOR_SPECIFIC_TUNNEL_FAILURE_REPORT);

      //SET_BIT (req.vendor_specific_node_report.dnn_tunnel_failure_report.grp.fields,
      //         USER_VENDOR_SPECIFIC_DNN);

      req.vendor_specific_node_report.specific_node_report_type = VENDOR_SPECIFIC_TUNNEL_RECOVERY_REPORT;
      req.vendor_specific_node_report.apn_dnn =
        format (req.vendor_specific_node_report.apn_dnn, "%s", dnn);
    }

  upf_pfcp_server_node_request_send (n, PFCP_NODE_REPORT_REQUEST, &req.grp);
}


static void
upf_detect_tunnel_failure (char *tunnel1)
{
  upf_main_t *gtm = &g_upf_main;

  upf_node_assoc_t *n;

  upf_debug("upf_detect_tunnel_failure !\n");
  pool_foreach (n, gtm->nodes, ({
                  upf_server_send_node_tunnel_report_req (
                      PATH_FAILURE, n - gtm->nodes,tunnel1);
                }));

}

static void
upf_detect_tunnel_recovery (char *tunnel1)
{
  upf_main_t *gtm = &g_upf_main;

  upf_node_assoc_t *n;

  upf_debug("upf_detect_tunnel_recovery !\n");
  pool_foreach (n, gtm->nodes, ({
                  upf_server_send_node_tunnel_report_req (
                      PATH_RECOVERY, n - gtm->nodes,tunnel1);
                }));

}


void add_gw_route(char *add_ip,char *del_ip)
{
    char cmd_str[128] = {0};
    sprintf(cmd_str,"route del default gw %s",del_ip);
    system(cmd_str);

    sprintf(cmd_str,"route del default gw %s",add_ip);
    system(cmd_str);

    sprintf(cmd_str,"route add default gw %s",add_ip);
    system(cmd_str);
}

extern upf_gw_tunnel_backup_t g_gw_ipsec_backup;
void *upf_gw_ipsec_backup_detect()
{

    char src_ip[64] = {0};
    char ip_str1[64] = {0};
    char ip_str2[64] = {0};
    char cmd_str1[128] = {0};
    char cmd_str2[128] = {0};
    char up_tunnel[256] = {0};
    char down_tunnel[256] = {0};

    u8 *a;
    FILE *fp = NULL;
    char buff[128] = {0};

    u8 reachable = 0;
    char intface[64] = {0};
    char tunnel1[64] = {0};
    char tunnel2[64] = {0};
    u8 *s = NULL;

    upf_gw_tunnel_backup_t *gb = &g_gw_ipsec_backup;

    strcpy(tunnel1,gb->tunnel1);
    strcpy(tunnel2,gb->tunnel2);
    strcpy(intface,gb->intface);


    if (ip46_address_is_ip4 (&gb->src_ip))
    {
        a = gb->src_ip.ip4.as_u8;
        sprintf (src_ip, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (src_ip, (char *)s);
        vec_free (s);
        s = NULL;
    }

    if (ip46_address_is_ip4 (&gb->gw1_a))
    {
        a = gb->gw1_a.ip4.as_u8;
        sprintf (ip_str1, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (ip_str1, (char *)s);
        vec_free (s);
        s = NULL;
    }

    if (ip46_address_is_ip4 (&gb->gw2_a))
    {
        a = gb->gw2_a.ip4.as_u8;
        sprintf (ip_str2, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw2_a.ip6);
        strcpy (ip_str2, (char *)s);
        vec_free (s);
        s = NULL;
    }

    reachable = 0;

    sprintf(cmd_str1,"ping %s -I %s -c 3 -i 0.1",ip_str1,intface);
    //sprintf(cmd_str1,"arping -i %s -S %s %s -c 3 -W 0.1",intface,src_ip,ip_str1);
    printf("cmd_str1 = %s\n",cmd_str1);

    fp = popen(cmd_str1,"r");
    if(fp != NULL)
    {
        while(fgets(buff,128,fp)!=NULL)
        {

            if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% packet loss")==NULL))
            {
                if(gb->tunnel2_added == 1)
                {
                    //sprintf(down_tunnel,"ipsec down %s",tunnel2);
                    //system(down_tunnel);
                    gb->tunnel2_added = 0;
                }
                if(gb->tunnel1_added == 0)
                {
                    sprintf(down_tunnel,"ipsec down %s",tunnel1);
                    system(down_tunnel);
                    sprintf(up_tunnel,"ipsec up %s",tunnel1);
                    system(up_tunnel);

                    add_gw_route(ip_str1,ip_str2);
                    gb->tunnel1_added = 1;

                }
                reachable = 1;
            }
        }
    }
    if(fp != NULL)
    {
        pclose(fp);
        fp = NULL;
    }

    if(reachable == 0)
    {
        if(gb->tunnel1_added == 1)
        {
            //sprintf(down_tunnel,"ipsec down %s",tunnel1);
            //system(down_tunnel);
            gb->tunnel1_added = 0;
        }
        sprintf(cmd_str2,"ping %s -I %s -c 3 -i 0.1",ip_str1,intface);
        //sprintf(cmd_str2,"arping -i %s -S %s %s -c 3 -W 0.1",intface,src_ip,ip_str2);
        fp = popen(cmd_str2,"r");
        if(fp != NULL)
        {
            while(fgets(buff,128,fp)!=NULL)
            {
                if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% packet loss")==NULL))
                {
                    if(gb->tunnel2_added == 0)
                    {
                        sprintf(down_tunnel,"ipsec down %s",tunnel2);
                        system(down_tunnel);
                        sprintf(up_tunnel,"ipsec up %s",tunnel2);
                        system(up_tunnel);

                        add_gw_route(ip_str2,ip_str1);
                        gb->tunnel2_added = 1;
                    }
                }
            }
        }
        if(fp != NULL)
        {
            pclose(fp);
            fp = NULL;
        }
    }
    gb->timer.base = unix_time_now ();
    start_first_thread_timer(UPF_GW_IPSEC_BACKUP_TIMER, UPF_GW_IPSEC_BACKUP_TIMER, &gb->timer);

    return NULL;
}


extern upf_gw_tunnel_backup_t g_gw_gre_backup;
void upf_gw_gre_backup_detect()
{
    char src_ip[64] = {0};
    char ip_str1[64] = {0};
    char ip_str2[64] = {0};
    char cmd_str1[128] = {0};
    char cmd_str2[128] = {0};
    char up_tunnel[256] = {0};
    char down_tunnel[256] = {0};

    u8 *a;
    FILE *fp = NULL;
    char buff[128] = {0};
    u8 reachable = 0;
    char intface[64] = {0};
    char tunnel1[64] = {0};
    char tunnel2[64] = {0};
    u8 *s = NULL;

    upf_gw_tunnel_backup_t *gb = &g_gw_gre_backup;

    strcpy(tunnel1,gb->tunnel1);
    strcpy(tunnel2,gb->tunnel2);

    strcpy(intface,gb->intface);

    if (ip46_address_is_ip4 (&gb->src_ip))
    {
        a = gb->src_ip.ip4.as_u8;
        sprintf (src_ip, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (src_ip, (char *)s);
        vec_free (s);
        s = NULL;
    }

    if (ip46_address_is_ip4 (&gb->gw1_a))
    {
        a = gb->gw1_a.ip4.as_u8;
        sprintf (ip_str1, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (ip_str1, (char *)s);
        vec_free (s);
        s = NULL;
    }

    if (ip46_address_is_ip4 (&gb->gw2_a))
    {
        a = gb->gw2_a.ip4.as_u8;
        sprintf (ip_str2, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw2_a.ip6);
        strcpy (ip_str2, (char *)s);
        vec_free (s);
        s = NULL;
    }

    reachable = 0;
    sprintf(cmd_str1,"arping -i %s -S %s %s -c 3 -W 0.1",intface,src_ip,ip_str1);

    fp = popen(cmd_str1,"r");
    if(fp != NULL)
    {
        while(fgets(buff,128,fp)!=NULL)
        {
            if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% unanswered")==NULL))
            {
                if(gb->tunnel2_added == 1)
                {
                    //sprintf(down_tunnel,"ipsec down %s",tunnel2);
                    //system(down_tunnel);
                    gb->tunnel2_added = 0;
                }
                if(gb->tunnel1_added == 0)
                {
                    sprintf(down_tunnel,"route del -net 0.0.0.0/0 dev %s",tunnel2);
                    system(down_tunnel);

                    sprintf(down_tunnel,"route del -net 0.0.0.0/0 dev %s",tunnel1);
                    system(down_tunnel);

                    sprintf(up_tunnel,"route add -net 0.0.0.0/0 dev %s",tunnel1);
                    system(up_tunnel);

                    gb->tunnel1_added = 1;

                }
                reachable = 1;
            }
        }
    }
    if(fp != NULL)
    {
        pclose(fp);
        fp = NULL;
    }

    if(reachable == 0)
    {
        if(gb->tunnel1_added == 1)
        {
            //sprintf(down_tunnel,"ipsec down %s",tunnel1);
            //system(down_tunnel);
            gb->tunnel1_added = 0;
        }
        sprintf(cmd_str2,"arping -i %s -S %s %s -c 3 -W 0.1",intface,src_ip,ip_str2);
        fp = popen(cmd_str2,"r");
        if(fp != NULL)
        {
            while(fgets(buff,128,fp)!=NULL)
            {
                if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% unanswered")==NULL))
                {
                    if(gb->tunnel2_added == 0)
                    {
                        sprintf(down_tunnel,"route del -net 0.0.0.0/0 dev %s",tunnel1);
                        system(down_tunnel);

                        sprintf(down_tunnel,"route del -net 0.0.0.0/0 dev %s",tunnel2);
                        system(down_tunnel);

                        sprintf(up_tunnel,"route add -net 0.0.0.0/0 dev %s",tunnel2);
                        system(up_tunnel);

                        gb->tunnel2_added = 1;
                    }
                }
            }
        }
        if(fp != NULL)
        {
            pclose(fp);
            fp = NULL;
        }
    }

    gb->timer.base = unix_time_now ();
    start_first_thread_timer(UPF_GW_GRE_BACKUP_TIMER, UPF_GW_GRE_BACKUP_TIMER, &gb->timer);

}

extern upf_gw_tunnel_backup_t g_single_gre_detect;
void upf_single_gre_detect()
{
    char ip_str1[64] = {0};
    char cmd_str1[128] = {0};

    char ip_str2[64] = {0};

    char up_tunnel[256] = {0};
    char down_tunnel[256] = {0};

    u8 *a;
    FILE *fp = NULL;
    char buff[128] = {0};
    u8 reachable = 0;
    char tunnel1[64] = {0};

    u8 *s = NULL;

    upf_gw_tunnel_backup_t *gb = &g_single_gre_detect;
    strcpy(tunnel1,gb->tunnel1);

    if (ip46_address_is_ip4 (&gb->gw1_a))
    {
        a = gb->gw1_a.ip4.as_u8;
        sprintf (ip_str1, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (ip_str1, (char *)s);
        vec_free (s);
        s = NULL;
    }


    reachable = 0;
    sprintf(cmd_str1,"arping %s -c 3 -W 0.1",ip_str1);
    get_lock();
    fp = popen(cmd_str1,"r");
    while(fgets(buff,128,fp)!=NULL)
    {
        if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% unanswered")==NULL))
        {
            if(gb->tunnel1_added == 0)
            {

                if (ip46_address_is_ip4 (&gb->dn_ser))
                {
                    a = gb->dn_ser.ip4.as_u8;
                    sprintf (ip_str2, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
                }
                else
                {
                    s = format (s, "%U", format_ip6_address, &gb->dn_ser.ip6);
                    strcpy (ip_str2, (char *)s);
                    vec_free (s);
                    s = NULL;
                }

                sprintf(down_tunnel,"route del -host %s dev %s",ip_str2,tunnel1);
                system(down_tunnel);

                sprintf(up_tunnel,"route add -host %s dev %s", ip_str2, tunnel1);
                system(up_tunnel);

                gb->tunnel1_added = 1;
                //send node report tunnel recover
                upf_detect_tunnel_recovery (tunnel1);

            }
            reachable = 1;
        }
    }
    if(fp != NULL)
    {
        pclose(fp);
        fp = NULL;
    }
    relea_lock();
    if(reachable == 0 && gb->tunnel1_added == 1)
    {
        //send node report request tunnel fauile
        upf_detect_tunnel_failure(tunnel1);
        gb->tunnel1_added = 0;

    }

    gb->timer.base = unix_time_now ();
    start_first_thread_timer(UPF_SINGLE_GRE_DETECT_TIMER, UPF_SINGLE_GRE_DETECT_TIMER, &gb->timer);

}

extern upf_gw_tunnel_backup_t g_single_ipsec_detect;
void upf_single_ipsec_detect()
{
    char ip_str1[64] = {0};
    char cmd_str1[128] = {0};

    char up_tunnel[256] = {0};
    char down_tunnel[256] = {0};

    u8 *a;
    FILE *fp = NULL;
    char buff[128] = {0};
    u8 reachable = 0;
    char tunnel1[64] = {0};

    u8 *s = NULL;

    upf_gw_tunnel_backup_t *gb = &g_single_ipsec_detect;
    strcpy(tunnel1,gb->tunnel1);

    if (ip46_address_is_ip4 (&gb->gw1_a))
    {
        a = gb->gw1_a.ip4.as_u8;
        sprintf (ip_str1, "%d.%d.%d.%d", a[0], a[1], a[2], a[3]);
    }
    else
    {
        s = format (s, "%U", format_ip6_address, &gb->gw1_a.ip6);
        strcpy (ip_str1, (char *)s);
        vec_free (s);
        s = NULL;
    }


    reachable = 0;
    sprintf(cmd_str1,"arping %s -c 3 -W 0.1",ip_str1);

    get_lock();
    fp = popen(cmd_str1,"r");
    while(fgets(buff,128,fp)!=NULL)
    {

        if((strstr(buff,"transmitted")!=NULL) && (strstr(buff,"100% unanswered")==NULL))
        {
            if(gb->tunnel1_added == 0)
            {
                //sprintf(down_tunnel,"ipsec down %s",tunnel1);
                sprintf(down_tunnel,"ipsec restart");
                system(down_tunnel);
                sprintf(up_tunnel,"ipsec up %s &",tunnel1);
                system(up_tunnel);

                //add_gw_route(ip_str1,ip_str2);
                gb->tunnel1_added = 1;
                 //send node report tunnel recover
                 upf_detect_tunnel_recovery (tunnel1);

            }
            reachable = 1;
        }
    }
    if(fp != NULL)
    {
        pclose(fp);
        fp = NULL;
    }
    relea_lock();

    if(reachable == 0 && gb->tunnel1_added == 1)
    {
        //send node report request tunnel fauile
        upf_detect_tunnel_failure(tunnel1);
        gb->tunnel1_added = 0;
    }

    gb->timer.base = unix_time_now ();
    start_first_thread_timer(UPF_SINGLE_IPSEC_DETECT_TIMER, UPF_SINGLE_IPSEC_DETECT_TIMER, &gb->timer);

}
//end liukang add for upf gre/ipsec tunnel detection 2022/06/22

void upf_run_independent_timer_reset (u32 time)
{
  upf_main_t *um = &g_upf_main;
  u32 id = UPF_RUN_INDEPENDENT_TIMER;

  if (um->run_independent_timer.handle)
    {
       upf_pfcp_server_timer_stop (um->run_independent_timer.handle);
       um->run_independent_timer.handle = 0;
    }

  um->run_independent_timer.base = unix_time_now ();
  um->run_independent_timer.period = time;

  upf_pfcp_server_timer_start (id, id, &um->run_independent_timer);
}

void upf_run_independent_alarm_report (alarm_status_e state)
{
  upf_main_t *um = &g_upf_main;

  if (state == UPF_ALARM_TYPE_RECOVER)
    {
      if (ALARM_IS_RECOVER(UPF_ALARM_RUN_INDEPENDENT))
        return;
    }
  else if (state == UPF_ALARM_TYPE_PRODUCE)
    {
      if (g_run_independent_switch == SWITCH_OFF)
        return;

      /* clear all pfcp association (and sessions) */
      upf_release_all_association ();
    }
  else
    return;

  upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
  memset (upf_alarm, 0, sizeof (*upf_alarm));
  upf_alarm->data = clib_mem_alloc_no_fail (sizeof(u64)); /* unused, but will be free at upf_pfcp_server_rx_alarm */

  upf_alarm->alarm_id = UPF_ALARM_RUN_INDEPENDENT;
  upf_alarm->alarm_type = state;
  upf_alarm->alarm_level = UPF_ALARM_LEVEL_SERIOUS;

  for (int i = 0; i < um->num_pfcp_threads; i++)
    {
      pfcp_send_sx_alarm_to_thread(i, upf_alarm);
    }

  ALARM_STATE(UPF_ALARM_RUN_INDEPENDENT) = state;
}

static clib_error_t *
iupf_run_independent_switch_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
  {
    if (unformat (input, "on"))
      {
        if (g_run_independent_switch != SWITCH_ON)
          upf_run_independent_timer_reset(g_run_independent_timer * HOUR_TO_SECONDS);

        g_run_independent_switch = SWITCH_ON;
      }
    else if (unformat (input, "off"))
      {
        g_run_independent_switch = SWITCH_OFF;
        upf_run_independent_alarm_report(UPF_ALARM_TYPE_RECOVER);
      }
    else if (unformat (input, "show"))
      {
        vlib_cli_output (vm, "switch is [%s]\n", g_run_independent_switch == SWITCH_ON ? "on" : "off");
        return 0;
      }
    else
      return 0;
  }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_run_independent_switch_command, static) = {
    .path = "upf run independent switch",
    .short_help = "upf run independent switch[on | off | show]",
    .function = iupf_run_independent_switch_command_fn,
};
/* *INDENT-OFF* */

static clib_error_t *
iupf_run_independent_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u32 value = 0;
  u32 unit = HOUR_TO_SECONDS;
  u32 is_shown = 0;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    {
      vlib_cli_output (vm, "     input none\n");
      return 0;
    }

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "set %u", &value))
        ;
      else if (unformat (line_input, "unit %u", &unit))
        ;
      else if (unformat (line_input, "show"))
        is_shown = 1;
      else
        {
          vlib_cli_output (vm, "     input error\n");
          unformat_free (line_input);
          return 0;
        }
    }
  unformat_free (line_input);

  if (value)
    {
      if (1 <= value && value <= 1024)
        g_run_independent_timer = value;

      upf_run_independent_alarm_report(UPF_ALARM_TYPE_RECOVER);
      upf_run_independent_timer_reset(g_run_independent_timer * unit);
    }

  if (is_shown)
    {
      vlib_cli_output (vm, " time is %uh", g_run_independent_timer);
    }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_run_independent_command, static) = {
    .path = "upf run independent timer",
    .short_help = "upf run independent timer[set <value> | show]",
    .function = iupf_run_independent_command_fn,
};
/* *INDENT-ON* */

upf_s_nssai_t *upf_s_nssai_lookup(u32 s_nssai)
{
    upf_s_nssai_t *t;
    vec_foreach (t, g_upf_main.nssai)
    {
        if (t->s_nssai == s_nssai)
            return t;
    }
    return NULL;
}

void upf_s_nssai_add_del (upf_s_nssai_t *t, u32 is_add)
{
    upf_main_t *um = &g_upf_main;
    if (is_add)
    {
        clib_spinlock_init (&t->lock);
        vec_add1 (um->nssai, *t);
    }
    else
    {
        clib_spinlock_free (&t->lock);
        vec_del1 (um->nssai, t - um->nssai);
    }
}

always_inline void upf_time_delay_record (upf_time_delay_t *t, u32 delay)
{
    t->delay_sum += delay;
    t->pkt_count ++;
    if (delay > t->curr_max_delay)
        t->curr_max_delay = delay;
}

void upf_psa_delay_per_s_nssai_record (upf_psa_delay_per_snssai_t *n, pfcp_qos_monitoring_measurement_t *measure)
{
    upf_time_delay_record (&n->dl_delay, measure->dl_pkt_delay);
    upf_time_delay_record (&n->ul_delay, measure->ul_pkt_delay);
    upf_time_delay_record (&n->rp_delay, measure->rp_pkt_delay);
}

always_inline void upf_time_delay_calc (upf_time_delay_t *n)
{
    upf_time_delay_t t = *n;
    if (t.pkt_count)
    {
        n->average_delay = t.delay_sum / t.pkt_count;
        n->max_delay = t.curr_max_delay;
    }

    n->pkt_count = 0;
    n->delay_sum = 0;
    n->curr_max_delay = 0;
}

void upf_psa_delay_per_s_nssai_update (void)
{
    upf_s_nssai_t *n;

    vec_foreach (n, g_upf_main.nssai)
    {
        upf_psa_delay_per_snssai_t *psa_delay = &n->psa_delay;
        upf_time_delay_calc (&psa_delay->dl_delay);
        upf_time_delay_calc (&psa_delay->ul_delay);
        upf_time_delay_calc (&psa_delay->rp_delay);
    }
}

void upf_psa_delay_timer_init (void)
{
    pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = 15 * 60; /* 15 minutes */
    upf_pfcp_server_timer_start (UPF_PSA_DELAY_TIMER, 0, &timer);
}

void upf_psa_delay_timer_handle (void)
{
    upf_psa_delay_per_s_nssai_update ();

    upf_psa_delay_timer_init();
}

u8 *upf_format_time_delay (u8 *s, va_list *args)
{
    upf_time_delay_t *v = va_arg (*args, upf_time_delay_t *);
    u32 verbose = va_arg (*args, u32);

    s = format (s, "average_delay: %-12u max_delay: %-12u\n", v->average_delay, v->max_delay);
    if (verbose)
    {
        s = format (s, "      curr_max_delay: %-10u  pkt_count: %-12u delay_count: %-12lu\n",
                v->curr_max_delay, v->pkt_count, v->delay_sum);
    }
    return s;
}

u8 *upf_format_delay_per_s_nssai (u8 *s, va_list *args)
{
    upf_psa_delay_per_snssai_t *v = va_arg (*args, upf_psa_delay_per_snssai_t *);
    u32 verbose = va_arg (*args, u32);

    s = format (s, "  RP: %U", upf_format_time_delay, &v->rp_delay, verbose);
    s = format (s, "  UL: %U", upf_format_time_delay, &v->ul_delay, verbose);
    s = format (s, "  DL: %U", upf_format_time_delay, &v->dl_delay, verbose);

    return s;
}

static clib_error_t *
iupf_show_delay_per_s_nssai_command_fn (vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    u32 nssai_value = 0;
    u32 limit = 10;
    u32 verbose = 0;
    upf_s_nssai_t *nssai = NULL;

    if (unformat_user (input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            if (unformat (line_input, "value 0x%x", &nssai_value))
                nssai = upf_s_nssai_lookup (nssai_value);
            else if (unformat (line_input, "value %u", &nssai_value))
                nssai = upf_s_nssai_lookup (nssai_value);
            else if (unformat (line_input, "limit %u", &limit))
                ;
            else if (unformat (line_input, "verbose"))
                 verbose = 1;
            else
            {
                vlib_cli_output (vm, "[err] input '%U'", format_unformat_error, line_input);
                break;
            }
        }
        unformat_free (line_input);
    }

    if (nssai_value)
    {
        if (nssai)
            vlib_cli_output (vm, "%U", upf_format_delay_per_s_nssai, &nssai->psa_delay, verbose);
        else
            vlib_cli_output (vm, "s-nssai value: %u not exist!\n", upf_format_s_nssai, &nssai_value);
        return NULL;
    }

    u32 i = 0;
    vec_foreach (nssai, g_upf_main.nssai)
    {
        vlib_cli_output (vm, "[%02d] S-NSSAI: %U\n%U", i++, upf_format_s_nssai, &nssai->s_nssai,
            upf_format_delay_per_s_nssai, &nssai->psa_delay, verbose);
        if (i >= limit)
            break;
    }
    vlib_cli_output (vm, "Total number of S-NSSAI is %u, the unit is millisecond!", vec_len(g_upf_main.nssai));

    return NULL;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_show_delay_per_s_nssai_command, static) = {
    .path = "show upf delay per s-nssai",
    .short_help = "show upf delay per s-nssai [value <> | limit <>]",
    .function = iupf_show_delay_per_s_nssai_command_fn,
};

void upf_statistics_timer_init(void)
{
	pfcp_time_t timer;
    timer.base = unix_time_now ();
    timer.period = g_statistics_update_timer_period; /* 5s */
    upf_pfcp_server_timer_start (UPF_STATISTICS_UPDATE_TIMER, 0, &timer);
	return;
}

void upf_global_statistics_timer_init(void)
{
	oam_time_t timer;
    timer.base = unix_time_now ();
    timer.period = g_upf_global_statistics_timer_period; /* 10s */
    upf_oam_server_timer_start (UPF_GLOBAL_STATISTICS_TIMER, 0, &timer);
	return;
}
u32 g_upf_status_report_timer_period = 0;
void upf_status_report_timer_init(void)
{
    if (0 == g_upf_status_report_timer_period)
    {
        return;
    }
	  oam_time_t timer;
    timer.base = unix_time_now ();
    timer.period = g_upf_status_report_timer_period; 
    upf_oam_server_timer_start (UPF_STATUS_REPORT_TIMER, 0, &timer);
	return;
}
static uword
upf_pfcp_timer_process (vlib_main_t *vm, vlib_node_runtime_t *rt, vlib_frame_t *f)
{
  u32 *expired = NULL;
  f64 now = unix_time_now ();
  u32 pool_index, timer_id;
  upf_main_t *um = &g_upf_main;
  upf_per_pfcp_thread_t *per_pfcp;
  upf_session_t *sx = NULL;

  per_pfcp = upf_get_per_pfcp ();
  if (per_pfcp == NULL)
    return 0;

  // upf_debug ("advancing wheel, now is %.3f", now);
  expired = tw_timer_expire_timers_vec_1t_3w_1024sl_ov (
      &per_pfcp->timer, vlib_time_now (vm), expired);
  // upf_debug ("Expired %d elements", vec_len (expired));

  for (int i = 0; i < vec_len (expired); i++)
    {
      /* Get session index and timer id */
      pool_index = expired[i] & 0x00FFFFFF;
      timer_id = expired[i] >> 24;

      switch (timer_id)
        {
        case PFCP_URR_TIMER:
            sx = sx_get_by_index(pool_index);
            if (sx)
              {
                upf_debug ("sx_index:%u wheel current tick: %u", pool_index, per_pfcp->timer.current_tick);
                upf_pfcp_session_timer_of_urr (sx, now);
              }
            break;

        // modify the heartbeat timer handling logic
        // added by caozhongwei 2025-07-11
        case PFCP_SERVER_HB_TIMER:
        {
          upf_node_assoc_t *n;

          // Get the specific node that triggered this timer
          if (PREDICT_FALSE(pool_is_free_index(um->nodes, pool_index)))
          {
            upf_err("Invalid node index %u for heartbeat timer", pool_index);
            break;
          }

          n = pool_elt_at_index(um->nodes, pool_index);

          // Check if heartbeat timeout duration is exceeded
          if (now - n->HB_timer.last_received > PFCP_HB_INTERVAL)
          {
            // Update N4 link status
            upf_check_n4_link_status(um, n, 0 /* heartbeat not received */);
          }

          // Check if we should continue sending heartbeats during N4 anomaly
          if (um->n4_persist.link_status == 0 && um->n4_persist.enabled)
          {
            if (now >= um->n4_persist.timeout_timestamp)
            {
              // Session persistence duration expired, release associated sessions
              upf_debug("N4 session persist timeout reached for CP node(%U), releasing association",
                       upf_format_node_id, &n->node_id);
              upf_pfcp_release_association(n);
              break;  // Don't send heartbeat, association is being released
            }
            else
            {
              // Continue sending heartbeats during persistence window
              upf_debug("N4 link anomalous, continuing heartbeat for CP node(%U) (%.2f seconds remaining)",
                       upf_format_node_id, &n->node_id, um->n4_persist.timeout_timestamp - now);
            }
          }

          // Send heartbeat request using existing function
          // Use the standard heartbeat sending function
          upf_server_send_heartbeat(pool_index);

          // Reset timer for next heartbeat
          n->HB_timer.base = now;
          upf_pfcp_server_timer_start(PFCP_SERVER_HB_TIMER, pool_index, &n->HB_timer);

          break;
        }
        case PFCP_SERVER_T1:
          upf_debug ("PFCP Server T1 Timeout: %u", pool_index);
          request_time_expired (pool_index);
          break;

        case PFCP_SERVER_RESPONSE:
          upf_debug ("PFCP Server Response Timeout: %u", pool_index);
          response_time_expired (pool_index);
          break;

        case PFCP_SERVER_DL_REPORT:
          {
            sx = sx_get_by_index(pool_index);
            if (!sx)
                break;
            upf_pdr_t *pdr;
            struct rules *active;
            active = upf_get_rules (sx, SX_ACTIVE);
            vec_foreach (pdr, active->pdr)
            {
              upf_far_t *far = upf_get_far_by_id (active, pdr->far_id);
              if (far->bar_id != ~0)
                {
                  if (far->downlink_buf_n)
                    upf_pfcp_downlink_data_report (sx, pdr);
                }
              else
                {
                  if (far->downlink_buf_n)
                    upf_pfcp_downlink_data_report (sx, pdr);
                }
            }
          }
          break;

        case GTPU_ECHO_REQ_TIMER:
          upf_debug ("GTPU_ECHO_REQ_TIMER Timeout: %u", pool_index);
          upf_server_send_gtpu_echo_req (pool_index);
          break;
        case GTPU_ECHO_REQ_T1:
          //upf_debug ("GTPU ECHO REQ T1 Timeout: %u", pool_index);
          gtpu_echo_req_time_expired (pool_index);
          break;
        case PFCP_SX_DATA_ACTIVE:
          {
            u32 si = pool_index;
            if (PREDICT_FALSE(pool_is_free_index (um->sessions, si)))
            {
                break;
            }
            sx = pool_elt_at_index (um->sessions, si);
            if (!(sx->up_inactive_timer.status & DATA_ACTIVE))
              {
                sx->up_inactive_timer.counter++;
                if ((sx->up_inactive_timer.counter * PFCP_HB_INTERVAL) >=
                    sx->up_inactive_timer.period)
                  {
                    upf_pfcp_user_plane_inactive_report (sx);
                    sx->up_inactive_timer.counter = 0;
                  }
              }
            else
              {
                clib_atomic_fetch_and (&sx->up_inactive_timer.status,
                                       ~DATA_ACTIVE);
                sx->up_inactive_timer.counter = 0;
              }
            sx->up_inactive_timer.timer.base = now;
            upf_pfcp_server_timer_start (PFCP_SX_DATA_ACTIVE, si,
                                         &sx->up_inactive_timer.timer);
          }
          break;

        case UPF_ALARM_TIMER:
          {
             upf_alarm_timer_of_db_agming();
			 upf_pfcp_msg_success_ratio_handle();

			 pfcp_time_t timer;
  		     timer.base = unix_time_now ();
  		     timer.period = 5;

			 upf_pfcp_server_timer_start (UPF_ALARM_TIMER, UPF_ALARM_TIMER, &timer);
          }
          break;
        /*add for dns sniffer begin by lixiao*/
        case DNS_SNIFFER_AGEING_TIMER:
            dns_sniffer_ageing_timer();
            break;
        /*add for dns sniffer end by lixiao*/
        case UPF_CPU_USAGE_TIMER:
            upf_cpu_usage_timer();
          break;
        // Add for dnn/s-nssai timeval by liupeng on 2021-11-16 below
        case DNN_TIMER:
          upf_send_dnn_usage();
          break;
        case S_NSSAI_TIMER:
          upf_send_s_nssai_usage();
          break;
        // Add for dnn/s-nssai timeval by liupeng on 2021-11-16 above
		case DSCP_SHARPE_REFRESH_TIMER:
		{
            iupf_bps_refreash();
            break;
		}
        // Add for dynmic_mac time refresh by liupeng on 2022-05-09 below
		case PFCP_DYNMIC_MAC_REFRESH_TIMER:
		  upf_dynmic_mac_state_refresh(vm);
		  break;
		case PFCP_DYNMIC_MAC_DELETE_TIMER:
		  upf_dynmic_mac_inactive_delete(vm);
		  break;
        // Add for dynmic_mac time refresh by liupeng on 2022-05-09 above
        case UPF_GW_IPSEC_BACKUP_TIMER:
          upf_gw_ipsec_backup_detect();
          break;

        case UPF_GW_GRE_BACKUP_TIMER:
          upf_gw_gre_backup_detect();
          break;

        case UPF_SINGLE_GRE_DETECT_TIMER:
          upf_single_gre_detect();
          break;

        case UPF_SINGLE_IPSEC_DETECT_TIMER:
          upf_single_ipsec_detect();
          break;

        case UPF_RUN_INDEPENDENT_TIMER:
          upf_run_independent_alarm_report(UPF_ALARM_TYPE_PRODUCE);
          break;

        case UPF_SRR_TIMER:
            sx = sx_get_by_index(pool_index);
            if (sx)
                upf_pfcp_session_srr_timer (sx, now);
            break;
        case UPF_ECHO_REQ_TIMER:
            upf_health_check(pool_index);
          break;

        case UPF_PSA_DELAY_TIMER:
            upf_psa_delay_timer_handle ();
            break;

        case UPF_FRER_DD_CHANGE_TIMER:
            upf_frer_dd_change_timer_handle ();
            break;

        case UPF_FRER_DD_AGING_TIMER:
            clib_bihash_foreach_key_value_pair_8_8(&g_upf_main.session_by_id, upf_frer_dd_aging_timer_handle, NULL);
            break;
        /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
        case UPF_PKT_RATE_MIN_TIMER:
        case UPF_PKT_RATE_TENTH_HOUR_TIMER:
        case UPF_PKT_RATE_HOUR_TIMER:
        case UPF_PKT_RATE_DAY_TIMER:
        case UPF_PKT_RATE_WEEK_TIMER:
            upf_pkt_rate_timer_handle(timer_id);
            break;
        /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */
        case UPF_REDIS_REGET_FAIL_LIST:
            upf_redis_reget_fail_list_timer();
            break;

		case UPF_STATISTICS_UPDATE_TIMER:
            upf_statistics_update_timer(vm);
            break;

        default:
          upf_debug ("timeout for unknown id: %u", expired[i] >> 24);
          break;
        }
    }

  if (expired)
    {
      _vec_len (expired) = 0;
    }

  return 0;
}

void upf_pfcp_ip_multicast_info_report(upf_report_ip_multicast_information_t *mip_info)
{
    pfcp_session_report_request_t req;
    struct rules *active;
    upf_urr_t *urr;
    u32 *urr_id;
    upf_node_assoc_t *assoc = NULL;
    upf_main_t *gtm = &g_upf_main;

    CHECK_POOL_IS_VALID_NORET(gtm->sessions, mip_info->sess_idx);
    upf_session_t *sx = pool_elt_at_index (gtm->sessions, mip_info->sess_idx);
    active = upf_get_rules (sx, SX_ACTIVE);
    upf_pdr_t *pdr = active->pdr + mip_info->pdr_idx;
    if (!pdr)
    {
        upf_err("Can not found pdr:%u", mip_info->pdr_idx);
        return;
    }

    if (vec_len (active->urr) == 0) /* how could that happen? */
      return;

    memset (&req, 0, sizeof (req));
    if (!pool_is_free_index (g_upf_main.nodes, sx->assoc.node))
      assoc = pool_elt_at_index (g_upf_main.nodes, sx->assoc.node);

    if (assoc && assoc->cp_feature & F_CPFF_LOAD)
      {

        if (upf_pfcp_load_info_report (&req.load_control_information))
          SET_BIT (req.grp.fields,
                   SESSION_REPORT_REQUEST_LOAD_CONTROL_INFORMATION);
      }
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
    req.report_type = REPORT_TYPE_USAR;

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_USAGE_REPORT);

    vec_foreach (urr_id, pdr->urr_ids)
    {
      urr = upf_get_urr_by_id (active, *urr_id);
      if (!urr)
      {
          upf_err("Can not found urr:%u", *urr_id);
          continue;
      }
      u32 trigger = 0;
      if (urr->triggers & REPORTING_TRIGGER_IP_MULTICAST_JOIN_LEAVE)
      {
          trigger |= USAGE_REPORT_TRIGGER_IP_MULTICAST_JOIN_LEAVE;
      }

      if (trigger != 0)
        {
          upf_usage_report_init (urr, trigger, &req.usage_report);
          _vec_len (req.usage_report)++;

          if (vec_len(mip_info->join_info))
          {
              req.usage_report->usage_report_trigger |= USAGE_REPORT_TRIGGER_IP_MULTICAST_JOIN_LEAVE;
              req.usage_report->join_ip_multicast_information = mip_info->join_info;
              SET_BIT (req.usage_report->grp.fields, USAGE_REPORT_JOIN_IP_MULTICAST_INFORMATION);
          }
          if (vec_len(mip_info->leave_info))
          {
              req.usage_report->usage_report_trigger |= USAGE_REPORT_TRIGGER_IP_MULTICAST_JOIN_LEAVE;
              req.usage_report->leave_ip_multicast_information = mip_info->leave_info;
              SET_BIT (req.usage_report->grp.fields, USAGE_REPORT_LEAVE_IP_MULTICAST_INFORMATION);
          }
        }
    }

    if (vec_len (req.usage_report) != 0)
      upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST,
                                            &req.grp);

    upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);
    if (req.usage_report)
      vec_free (req.usage_report);
}

void upf_multicast_address_report(upf_multicast_addr_t *ip_m, u32 session_idx, u32 pdr_idx)
{
    upf_report_ip_multicast_information_t report = {0};
    upf_multicast_group_t *group;

    if (!ip_m || !vec_len(ip_m->group))
        return;

    vec_foreach(group, ip_m->group)
    {
        if (group->type == MULTICAST_JOIN)
        {
            pfcp_join_ip_multicast_information_t *join;
            APPEND_NEW_MEMBER(report.join_info, join);
            SET_BIT (join->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
            if (ip46_address_is_ip4(&group->group_addr))
            {
                join->mip.ip4_start = group->group_addr.ip4;
                join->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
            }
            else
            {
                join->mip.ip6_start = group->group_addr.ip6;
                join->mip.flags |= IE_IP_MULTICAST_ADDRESS_V6;
            }
        }
        else
        {
            pfcp_leave_ip_multicast_information_t *leave;
            APPEND_NEW_MEMBER(report.leave_info, leave);
            SET_BIT (leave->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
            if (ip46_address_is_ip4(&group->group_addr))
            {
                leave->mip.ip4_start = group->group_addr.ip4;
                leave->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
            }
            else
            {
                leave->mip.ip6_start = group->group_addr.ip6;
                leave->mip.flags |= IE_IP_MULTICAST_ADDRESS_V6;
            }
        }
    }

    report.pdr_idx = pdr_idx;
    report.sess_idx = session_idx;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    msg->data = clib_mem_alloc_aligned_no_fail (sizeof(report), CLIB_CACHE_LINE_BYTES);
    memset (msg->data, 0, sizeof((report)));
    msg->msg_id = PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST;
    memcpy(msg->data, &(report), sizeof((report)));

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST,\n");
        send_sx_msg_to_pfcp_thread(i, msg);
    }
}

void upf_pfcp_mac_address_info_report(upf_report_mac_information_t *eth_info)
{
    pfcp_session_report_request_t req;
    struct rules *active;
    upf_urr_t *urr;
    u32 *urr_id;
    upf_node_assoc_t *assoc = NULL;
    upf_main_t *gtm = &g_upf_main;

    CHECK_POOL_IS_VALID_NORET(gtm->sessions, eth_info->sess_idx);
    upf_session_t *sx = pool_elt_at_index (gtm->sessions, eth_info->sess_idx);
    active = upf_get_rules (sx, SX_ACTIVE);
    upf_pdr_t *pdr = active->pdr + eth_info->pdr_idx;
    if (!pdr)
    {
        upf_err("Can not found pdr:%u", eth_info->pdr_idx);
        return;
    }

    if (vec_len (active->urr) == 0) /* how could that happen? */
        return;

    memset (&req, 0, sizeof (req));
    if (!pool_is_free_index (g_upf_main.nodes, sx->assoc.node))
        assoc = pool_elt_at_index (g_upf_main.nodes, sx->assoc.node);

    if (assoc && assoc->cp_feature & F_CPFF_LOAD)
    {
        if (upf_pfcp_load_info_report (&req.load_control_information))
            SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_LOAD_CONTROL_INFORMATION);
    }
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
    req.report_type = REPORT_TYPE_USAR;

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_USAGE_REPORT);

    vec_foreach (urr_id, pdr->urr_ids)
    {
        urr = upf_get_urr_by_id (active, *urr_id);
        if (!urr)
        {
            upf_err("Can not found urr:%u", *urr_id);
            continue;
        }
        u32 trigger = 0;
        if (urr->triggers & REPORTING_TRIGGER_MAC_ADDRESSES_REPORTING)
        {
            trigger |= USAGE_REPORT_TRIGGER_MAC_ADDRESSES_REPORTING;
        }

        if (trigger != 0)
        {
            upf_usage_report_init (urr, trigger, &req.usage_report);
            _vec_len (req.usage_report)++;
            SET_BIT (req.usage_report->grp.fields, USAGE_REPORT_ETHERNET_TRAFFIC_INFORMATION);
            if (eth_info->is_detect)
            {
                SET_BIT (req.usage_report->ethernet_traffic_information.grp.fields, ETHERNET_TRAFFIC_INFORMATION_MAC_ADDRESSES_DETECTED);
                req.usage_report->ethernet_traffic_information.mac_addresses_detected = eth_info->addr;
            }
            else
            {
                SET_BIT (req.usage_report->ethernet_traffic_information.grp.fields, ETHERNET_TRAFFIC_INFORMATION_MAC_ADDRESSES_REMOVED);
                req.usage_report->ethernet_traffic_information.mac_addresses_removed = eth_info->addr;
            }
        }
    }

    if (vec_len (req.usage_report) != 0)
        upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp);

    upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);
    if (req.usage_report)
        vec_free (req.usage_report);
}

void upf_pfcp_traf_inactive_detect_report(upf_inner_report_info_t *info)
{
    upf_session_t *sx = sx_get_by_index(info->sess_idx);
    if (!sx)
    {
        upf_debug("Can not found sess_idx:%u", info->sess_idx);
        return;
    }

    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    upf_pdr_t *pdr = NULL;
    if (info->has_pdrid)
    {
        pdr = upf_get_pdr_by_id (active, info->pdr_id);
        if (!pdr)
        {
            upf_debug("Can not found pdr:%u", info->pdr_id);
            return;
        }
    }

    pfcp_session_report_request_t req;
    memset (&req, 0, sizeof (req));

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
    req.report_type = REPORT_TYPE_TIDR;
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_INSPUR_TRAFFIC_INACTIVE_REPORT);

    if (info->has_pdrid)
    {
        req.traffic_inactive_report.flags |= F_PDR;
        req.traffic_inactive_report.pdr_id = info->pdr_id;
    }
    req.traffic_inactive_report.type = info->traf_inact_detect.type;
    req.traffic_inactive_report.urr_id = info->urr_id;

    upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp);

    upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);
}

void upf_pfcp_per_qos_flow_qos_monitor_report(upf_inner_report_info_t *info)
{
    upf_session_t *sx = sx_get_by_index(info->sess_idx);
    if (!sx)
    {
        upf_debug("Can not found sess_idx:%u", info->sess_idx);
        return;
    }

    if (!info->has_srrid)
        return;
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    upf_srr_t *srr = upf_get_srr_by_id (active, info->srr_id);
    if (!srr)
    {
        upf_debug("Can not found srr:%u", info->srr_id);
        return;
    }

    pfcp_session_report_t sx_report = {0};
    SET_BIT (sx_report.grp.fields, SESSION_REPORT_SRR_ID);
    sx_report.srr_id = info->srr_id;
    SET_BIT (sx_report.grp.fields, SESSION_REPORT_QOS_MONITORING);
    vec_add1(sx_report.qos_monitor_report, info->per_qos_monitor_report);

    pfcp_session_report_request_t req = {0};
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
    req.report_type = REPORT_TYPE_SESR;
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_SESSION_REPORT);
    vec_add1(req.session_report, sx_report);

    upf_pfcp_server_session_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp);

    upf_pfcp_free_msg (PFCP_SESSION_REPORT_REQUEST, &req.grp);
}

void upf_pfcp_input_handle (vlib_main_t *vm, vlib_buffer_t *b, int is_ip4)
{
  upf_main_t *gtm = &g_upf_main;
  ip4_header_t *ip4;
  ip6_header_t *ip6;
  sx_msg_t *msg;
  pfcp_offending_ie_t *err = NULL;

  /* udp_local hands us a pointer to the udp data */
  u8 *data = vlib_buffer_get_current (b);
  udp_header_t *udp = (udp_header_t *)(data - sizeof (*udp));

  if (PREDICT_FALSE(clib_net_to_host_u16 (udp->length) < sizeof (udp_header_t) + 9))
    {
      upf_info ("pfcp data len check fail! udp len:%d", clib_net_to_host_u16 (udp->length));
      return;
    }
  u16 data_len = clib_net_to_host_u16 (udp->length) - sizeof (udp_header_t);

  ip46_sd_t ip46 = {0};
  if (is_ip4)
    {
      /* $$$$ fixme: udp_local doesn't do ip options correctly anyhow */
      ip4 = (ip4_header_t *)(((u8 *)udp) - sizeof (*ip4));
      ip_set (&ip46.dst, &ip4->dst_address, is_ip4);
      ip_set (&ip46.src, &ip4->src_address, is_ip4);
    }
  else
    {
      ip6 = (ip6_header_t *)(((u8 *)udp) - sizeof (*ip6));
      ip_set (&ip46.dst, &ip6->dst_address, is_ip4);
      ip_set (&ip46.src, &ip6->src_address, is_ip4);
    }

  /* check pfcp endpoint */
  uword *p = hash_get_mem (gtm->pfcp_endpoint_index, &ip46.dst);
  if (!p)
    {
      upf_err ("pfcp endpoint not found for %U", format_ip46_address, &ip46.dst);
      return;
    }

  if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
  {
      msg = malloc(sizeof (*msg));
      memset (msg, 0, sizeof (*msg));
      msg->data = malloc (data_len);
  }
  else
  {
      msg = clib_mem_alloc_aligned_no_fail (sizeof (*msg), CLIB_CACHE_LINE_BYTES);
      memset (msg, 0, sizeof (*msg));
      msg->data = vec_new (u8, data_len);
  }
  vlib_buffer_contents (vm, vlib_get_buffer_index (vm, b), msg->data);

  msg->fib_index = vnet_buffer (b)->ip.fib_index;
  msg->lcl.address = ip46.dst;
  msg->rmt.address = ip46.src;
  msg->lcl.port = udp->dst_port;
  msg->rmt.port = udp->src_port;
  msg->pfcp_endpoint = p[0];

  upf_pfcp_statistic (msg->hdr->type, 1, DIRECTION_RX);

  upf_debug ("rcv pfcp from %p %U, src_port:%u type %d", msg, format_ip46_sd, &ip46, clib_net_to_host_u16 (msg->rmt.port), msg->hdr->type);
  u64 cp_seid = 0;
  if (!msg->hdr->s_flag)
    {
      cp_seid = 0;
    }
  else if (0 == msg->hdr->session_hdr.seid &&
           msg->hdr->type == PFCP_SESSION_ESTABLISHMENT_REQUEST)
    {
      union
      {
        struct pfcp_group grp;
        pfcp_session_establishment_request_t session_establishment_request;
      } m;
      int r = 0;
      memset (&m, 0, sizeof (m));
      r = upf_pfcp_decode_msg (msg->hdr->type, &msg->hdr->session_hdr.ies[0],
                           clib_net_to_host_u16 (msg->hdr->length) -
                               sizeof (msg->hdr->session_hdr),
                           &m.grp, &err);
      if (r != 0)
        {
          upf_info ("PFCP: pre-Decode session request fail! message type: %d, "
                    "cause=%d",
                    msg->hdr->type, r);
          vec_free(err);
        }
      cp_seid = m.session_establishment_request.f_seid.seid;

      if (r == 0)
      {
          msg->s_nssai = m.session_establishment_request.s_nssai;
          upf_dnn_init2 (&m.session_establishment_request, msg);

          upf_dnn_pfcp_statistic (msg->dnn, msg->hdr->type, 0, DIRECTION_RX);
          upf_nwi_pfcp_statistic (msg->s_nssai, msg->hdr->type, 0, DIRECTION_RX);
      }

      memcpy(&msg->user_id, &m.session_establishment_request.user_id, sizeof(msg->user_id));
      msg->user_id.nai = NULL;
      upf_pfcp_free_msg (msg->hdr->type, &m.grp);
    }
  else
    {
      upf_session_t *sess;
      if (!(sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid))))
        {
          upf_info ("Sx Session %x not found.\n",
                    be64toh (msg->hdr->session_hdr.seid));
        }
      else
        {
          cp_seid = sess->cp_seid;

          msg->s_nssai = sess->s_nssai;
          msg->dnn = format (msg->dnn, "%s", sess->dnn);

          upf_dnn_pfcp_statistic (msg->dnn, msg->hdr->type, 0, DIRECTION_RX);
          upf_nwi_pfcp_statistic (msg->s_nssai, msg->hdr->type, 0, DIRECTION_RX);

#if 0 /*just for openupf test, trace imsi*/
          if (sess->user_id.flags & USER_ID_IMSI)
            {
              union
              {
                struct pfcp_group grp;
                pfcp_simple_response_t simple_response;
                pfcp_session_set_deletion_request_t
                    session_set_deletion_request;
                pfcp_session_establishment_request_t
                    session_establishment_request;
                pfcp_session_establishment_response_t
                    session_establishment_response;
                pfcp_session_modification_request_t
                    session_modification_request;
                pfcp_session_modification_response_t
                    session_modification_response;
                pfcp_session_deletion_request_t session_deletion_request;
                pfcp_session_deletion_response_t session_deletion_response;
                pfcp_session_report_request_t session_report_request;
                pfcp_session_report_response_t session_report_response;
              } m;

              memset (&m, 0, sizeof (m));
              upf_log_ex ("imsi: %s msg_type: %d \n", sess->user_id.imsi_str,
                          msg->hdr->type);
              int r = upf_decode_group (&msg->hdr->session_hdr.ies[0],
                            clib_net_to_host_u16 (msg->hdr->length) -
                                sizeof (msg->hdr->session_hdr),
                            &upf_msg_specs[msg->hdr->type], &m.grp, &err,
                            sess->user_id.imsi_str);
              if (r)
              {
                  upf_info ("PFCP: pre-Decode fail! message type: %d cause=%d, up_seid:0x%lx",
                            msg->hdr->type, r, msg->hdr->session_hdr.seid);
                  vec_free(err);
              }
              upf_log_ex (
                  "imsi: %s code:\n%U\n", sess->user_id.imsi_str, format_hex,
                  (u8 *)(data - sizeof (udp_header_t) - sizeof (ip4_header_t) -
                         sizeof (ethernet_header_t)),
                  b->current_length + sizeof (udp_header_t) +
                      sizeof (ip4_header_t) + sizeof (ethernet_header_t));
              upf_pfcp_free_msg (msg->hdr->type, &m.grp);
            }
#endif
        }
    }

  if (g_single_trace_flag)
  {
      u32 pre_hdr_len = sizeof (udp_header_t) +  sizeof (ethernet_header_t);
      if (is_ip4)
        pre_hdr_len += sizeof (ip4_header_t);
      else
        pre_hdr_len += sizeof (ip6_header_t);
      u32 total_len = pre_hdr_len + b->current_length;

      u8 *pfcp_trace_data = NULL;
      if (g_upf_anonymization_switch == SWITCH_ON)
      {
          pfcp_trace_data = vec_new (u8, total_len);
          memcpy (pfcp_trace_data, data - pre_hdr_len, total_len);
          upf_pfcp_trace_data_anonymization(pfcp_trace_data + pre_hdr_len, b->current_length);
      }
      else
        pfcp_trace_data = data - pre_hdr_len;

      upf_session_t *sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid));
      if (sess)
      {
        memcpy (&msg->user_id, &sess->user_id, sizeof(msg->user_id));
        msg->user_id.nai = NULL;
        if (sess->single_trace_flag != g_single_trace_flag)
        {
          upf_debug("pfcp single trace updata\n");
          upf_pfcp_single_trace_update(sess, msg, is_ip4);
        }
        upf_debug("######## PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, msg_type:%s\n", upf_pfcp_msg_desc[msg->hdr->type]);
        upf_pfcp_single_trace_push(sess, msg, pfcp_trace_data, total_len, UPF_PKT_DIRECTION_IN);
      }
      else
      {
        upf_debug("######## PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, msg_type:%s\n", upf_pfcp_msg_desc[msg->hdr->type]);
        upf_pfcp_node_single_trace_push(msg, pfcp_trace_data, total_len, is_ip4, UPF_PKT_DIRECTION_IN);
      }

      if (g_upf_anonymization_switch == SWITCH_ON)
      {
        vec_free (pfcp_trace_data);
      }
  }

  u32 thread_index = cp_seid % gtm->num_pfcp_threads;
  pfcp_thread_send_sx_msg_to_thread (thread_index, msg);

  return;
}

void
upf_pfcp_server_session_report_usage (upf_session_t *sess, u32 pdr_idx)
{
  upf_main_t *gtm = &g_upf_main;
  sx_event_t *event = clib_mem_alloc_no_fail (sizeof (sx_event_t));
  memset (event, 0, sizeof (sx_event_t));
  event->event_type = EVENT_URR;
  event->si = (uword) (sess - gtm->sessions);

  /* neil.fan@20220803 add this parameter to support APP ID for urr report refers to 29244 5.4.11,
   * a packet has one and only one pdr and maybe relates to several urrs.
   */
  event->opaque = pdr_idx;

  pfcp_thread_send_sx_event_to_thread (
      sess->thread_index - g_upf_main.first_pfcp_thread_index, event);

  upf_debug ("sending URR event on %wd\n", (uword) (sess - gtm->sessions));
  // vlib_process_signal_event_mt(vm, sx_api_process_node.index, EVENT_URR,
  //                            (uword)(sess - gtm->sessions));
}

void
upf_pfcp_delay_downlink_report (upf_session_t *sess, u32 notification_delay)
{
  upf_main_t *um = &g_upf_main;
  sx_event_t *event;
  event = clib_mem_alloc_no_fail (sizeof (sx_event_t));
  memset (event, 0, sizeof (sx_event_t));
  event->event_type = EVENT_DL_REPORT;
  event->si = (uword) (sess - um->sessions);
  event->opaque = notification_delay * 50 / 1000;
  pfcp_thread_send_sx_event_to_thread (
      sess->thread_index - g_upf_main.first_pfcp_thread_index, event);

  sess->session_report_time = unix_time_now_nsec();
  upf_debug ("sending EVENT_DL_REPORT event on %wd\n",
             (uword) (sess - um->sessions));
  upf_debug(" upf_pfcp_delay_downlink_report ueip: %U", format_ip46_address, &sess->ue_address, IP46_TYPE_ANY);
}

int
upf_pfcp_server_event_rx (sx_event_t *event)
{
  sx_server_main_t *sxsm = &sx_server_main;
  upf_main_t *gtm = &g_upf_main;

  switch (event->event_type)
    {
    case EVENT_TX:
      {
        sx_msg_t *tx = (sx_msg_t *)event->msg;

        if (!pool_is_free_index (gtm->nodes, tx->node))
          {
            sx_msg_t *msg;

            clib_spinlock_lock (&sxsm->lock);
            pool_get_aligned (sxsm->msg_pool, msg, CLIB_CACHE_LINE_BYTES);
            clib_spinlock_unlock (&sxsm->lock);

            *msg = *tx;

            upf_pfcp_server_request_send (msg);
          }
        else
          {
            vec_free (tx->data);
            vec_free (tx->dnn);
          }
        clib_mem_free (tx);

        break;
      }

    case EVENT_URR:
      {
        uword si = (uword)event->si;
        upf_session_t *sx;
        if (!pool_is_free_index (gtm->sessions, si))
          {
            sx = pool_elt_at_index (gtm->sessions, si);
            // upf_debug ("URR Event on Session Idx: %wd, %p\n", si,
            // sx);
            upf_pfcp_session_report_usage (sx, sxsm->now, event->opaque);
          }
        break;
      }

    case EVENT_DL_REPORT:
      {
        u32 sidx = event->si;
        pfcp_time_t timer;
        timer.base = unix_time_now ();
        timer.period = event->opaque;
        upf_pfcp_server_timer_start (PFCP_SERVER_DL_REPORT, sidx, &timer);
        break;
      }

    default:
      upf_debug ("event %ld, %p. ", event->event_type, event->msg);
      break;
    }

  clib_mem_free (event);

  return 0;
}

int
upf_pfcp_server_rx_alarm (upf_alarm_msg_t *alarm)
{

  upf_alarm_notify(alarm->alarm_id, alarm->alarm_type, alarm->alarm_level, alarm->data);
  clib_mem_free (alarm->data);
  clib_mem_free (alarm);

  return 0;
}

void upf_l2_unicast_hash_handle(void *data)
{
    if (!data)
        return;

    upf_l2_msg_t *msg_buf = (upf_l2_msg_t *)data;
    upf_l2_forw_t *l2_forw = &msg_buf->l2_forw;
    upf_main_t *um = &g_upf_main;

    u8 field = msg_buf->field;
    if (field & L2_FORW_MSG_FEILD_N6)
    {
        upf_l2_unicast_hash_add_del(&um->hash_eth_unicast_by_mac, l2_forw, ADD);
    }

    if (field & L2_FORW_MSG_FEILD_VN)
    {
        /* neil.fan@20220519 add: learning 5G VN Internal L2 forwarding table at the same time, just update the nwi index */
        if (l2_forw->port_index < UPF_L2_INVALID_IDX_START)
        {
            upf_session_t *sx = sx_get_by_index(l2_forw->port_index);
            if (sx && sx->vn_nwi_name)
            {
                l2_forw->key.nwi_idx = sx->vn_nwi_index;
                upf_l2_unicast_hash_add_del(&um->hash_vn_eth_unicast_by_mac, l2_forw, ADD);
            }
        }
    }
}

void upf_clear_buff_handle(void *data)
{   
    if (!data)
        return;

    upf_clear_buff_to_pfcp_t *msg_buf = (upf_clear_buff_to_pfcp_t *)data;
    u32 sess_index = msg_buf->session_index;
    u32 far_id = msg_buf->far_id;

    upf_session_t *sess = NULL;
    struct rules *active = NULL;
    upf_far_t *far = NULL;
    sess = sx_get_by_index(sess_index);

    if(!sess)
    {
        upf_err("upf get buffer clear sess failed, sess_idx:%u\n", sess_index);
        return;
    }

    clib_spinlock_lock (&sess->lock);
    UPF_PDU_SESS_STATISTICS_ADD(sess, REV_BUFFER_CLEAR_TIMES);
    UPF_STATISTICS_ADD(REV_BUFFER_CLEAR_TIMES);
    active = upf_get_rules (sess, SX_ACTIVE);
    far = upf_get_far_by_id (active, far_id);

    if(!far)
    {
        upf_err("upf get buffer clear far failed, far_id:%u\n", far_id);
        clib_spinlock_unlock (&sess->lock);
        return;
    }
    clib_spinlock_unlock (&sess->lock);
    upf_pfcp_clean_far_buffering_list(sess, far); 
}

void upf_broadcast_hash_handle(void *data)
{
 if (!data)
        return;

    upf_broadcast_msg_t *msg_buf = (upf_broadcast_msg_t *)data;
    upf_broadcast_t *broadcast = &msg_buf->broadcast;
    upf_main_t *um = &g_upf_main;

    if (broadcast->sess_index != (u32)~0)
    {
        upf_broadcast_hash_add_del(&um->hash_eth_broadcast_by_key, broadcast, ADD);
        return ;
    }
    upf_err("upf_broadcast_hash_handle invalid sess index!");
}

int upf_pfcp_server_msg_handle (upf_send_msg_t *msg)
{
    switch (msg->msg_id)
    {
        case PFCP_RPC_PUBLISH_ADD_DNN_USAGE:
        {
            // Modify for dnn/s-nssai usage from vpp-agent to upf by liupeng on 2021-11-15 below
            upf_pfcp_events_publish (msg->msg_id, NULL, msg->data);
            // Modify for dnn/s-nssai usage from vpp-agent to upf by liupeng on 2021-11-15 above
            break;
        }
        case PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST:
        {
            upf_pfcp_ip_multicast_info_report((upf_report_ip_multicast_information_t *)msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_ETH_BROADCAST:
        {
            upf_add_member_to_broadcast_table((upf_eth_broadcast_mem_report_t *)msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_ETH_MULTICAST_ADD:
        {
            upf_add_member_to_multicast_table((upf_eth_multicast_t *)msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_ETH_MULTICAST_DEL:
        {
            upf_del_eth_multicast_domain((upf_eth_multicast_key_t *)msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_L2_FORW_TABLE:
        {
            upf_l2_unicast_hash_handle(msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_BROADCAST_IP_TABLE:
        {
            upf_broadcast_hash_handle(msg->data);
            break;
        }

        case PFCP_RPC_PUBLISH_REPORT_MAC_ADDRESS_REPORT:
        {
            upf_pfcp_mac_address_info_report(msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_TRAF_INACT_DETECT_REPORT:
        {
            upf_pfcp_traf_inactive_detect_report(msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_PER_QOS_MONITOR_REPORT:
        {
            upf_pfcp_per_qos_flow_qos_monitor_report(msg->data);
            break;
        }
        case PFCP_RPC_PUBLISH_FRER_MAC_SESS_TABLE:
        {
            upf_frer_mac_sess_hash_add_del(&g_upf_main.hash_frer_sess_by_mac, msg->data, ADD);
            break;
        }
        case PFCP_RPC_PUBLISH_DEL_BUFFER:
        {
            upf_clear_buff_handle(msg->data);
            break;
        }
    }
    clib_mem_free (msg->data);
    clib_mem_free (msg);

    return 0;
}

int upf_pfcp_server_handle_s_nssai_msg (upf_send_msg_t *msg)
{
  // Modify for dnn/s-nssai usage from vpp-agent to upf by liupeng on 2021-11-15 below
  upf_pfcp_events_publish (PFCP_RPC_PUBLISH_ADD_S_NSSAI_USAGE, NULL, msg->data);
  // Modify for dnn/s-nssai usage from vpp-agent to upf by liupeng on 2021-11-15 above
  clib_mem_free (msg->data);
  clib_mem_free (msg);

  return 0;
}

int upf_pfcp_thread_handle_msg (upf_thread_msg_t *msg)
{
    switch (msg->msg_id)
    {
        case PFCP_RPC_PUBLISH_VN_MULTICAST_PROTO:
            upf_handle_vn_multicast_proto (msg->as_u64);
            break;

        default:
            break;
    }

    clib_mem_free (msg);
    return 0;
}

/*********************************************************/

clib_error_t *
sx_init_server_main (vlib_main_t *vm)
{
  sx_server_main_t *sx = &sx_server_main;
  clib_error_t *error;

  if ((error = vlib_call_init_function (vm, vnet_interface_cli_init)))
    return error;

  sx->vlib_main = vm;
  sx->start_time = time (NULL);

  // pool_init_fixed (sx->msg_pool, UPF_MAX_POOL);

  clib_spinlock_init (&sx->lock);

  udp_register_dst_port (vm, PFCP_UDP_DST_PORT, sx4_input_node.index,
                         /* is_ip4 */ 1);
  udp_register_dst_port (vm, PFCP_UDP_DST_PORT, sx6_input_node.index,
                         /* is_ip4 */ 0);
  clib_bihash_init_8_8 (&sx->request_q, "sx_request_q", UPF_MAPPING_BUCKETS,
                        UPF_MAPPING_MEMORY_SIZE);
  clib_bihash_init_40_8 (&sx->response_q, "sx_response_q", UPF_MAPPING_BUCKETS,
                         UPF_MAPPING_MEMORY_SIZE);

  upf_info ("PFCP: start_time: %p, %d, %x.", sx, sx->start_time,
            sx->start_time);
  return 0;
}

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (sx_api_process_node) = {
    .function = sx_process,
    .type = VLIB_NODE_TYPE_PROCESS,
    .process_log2_n_stack_bytes = 18,
    .runtime_data_bytes = sizeof (void *),
    .name = "sx-api",
    .n_errors = SX_PROCESS_N_ERROR,
    .error_strings = g_sx_process_error_strings,
};

VLIB_REGISTER_NODE (pfcp_timer_node, static) = {
    .function = upf_pfcp_timer_process,
    .name = "pfcp-timer",
    .type = VLIB_NODE_TYPE_INPUT,
    .state = VLIB_NODE_STATE_INTERRUPT,
};
/* *INDENT-ON* */

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
