# N4 Persist逻辑Bug修复报告

## 问题描述

用户发现在设置了很大的persist time或max-missed-heartbeats值时，show命令显示的结果不合理：

```
session persistence configuration:
  Status: enabled
  Persistence time: 1410065408 seconds
  Max missed heartbeats: 50000
  Current link status: anomaly
  Current missed heartbeats: 362
  Session persist time expired
```

问题分析：
1. **Current link status显示为anomaly不合理**：当前心跳丢失次数是362，而最大允许丢失次数是50000，按逻辑应该显示为normal
2. **Session persist time expired显示错误**：设置了很大的persist time，不应该显示为expired

## 根本原因分析

### 1. link_status状态恢复逻辑缺失

在`upf_check_n4_link_status`函数中：
- 当心跳丢失次数达到阈值时，`link_status`会被设置为0（异常状态）
- **但是当心跳丢失次数没有达到阈值时，代码没有将`link_status`重新设置为1（正常状态）**

这导致一旦link_status被设置为0，即使后来调整了max_missed_heartbeats参数使得当前丢失次数小于阈值，link_status也不会自动恢复为1。

### 2. Show命令判断逻辑错误

原始show命令逻辑：
```c
vlib_cli_output (vm, "  Current link status: %s",
                gtm->n4_persist.link_status ? "normal" : "anomaly");

if (gtm->n4_persist.link_status == 0 && gtm->n4_persist.enabled)
{
  // 显示persist信息
}
```

这个逻辑直接使用了`link_status`字段，但没有考虑当前实际的心跳丢失情况。

## 修复方案

### 1. 修复心跳检查逻辑

在`upf_check_n4_link_status`函数中添加状态恢复逻辑：

```c
if (gtm->n4_persist.missed_heartbeats >= gtm->n4_persist.max_missed_heartbeats)
{
  // 只有在当前状态为正常时才转换为异常状态
  if (gtm->n4_persist.link_status == 1)
  {
    gtm->n4_persist.link_status = 0;
    // 设置timeout_timestamp等
  }
}
else
{
  // 新增：当心跳丢失次数小于阈值时，恢复为正常状态
  if (gtm->n4_persist.link_status == 0)
  {
    gtm->n4_persist.link_status = 1;
    gtm->n4_persist.timeout_timestamp = 0;
    // 清除告警等
  }
}
```

### 2. 修复Show命令逻辑

修改show命令，基于实际的心跳丢失情况判断状态：

```c
// 基于实际逻辑确定链路状态
const char* link_status_str;
if (gtm->n4_persist.missed_heartbeats >= gtm->n4_persist.max_missed_heartbeats)
{
  link_status_str = "anomaly";
}
else
{
  link_status_str = "normal";
}

// 只有在实际处于异常状态且启用持久化时才显示持久化信息
if (gtm->n4_persist.missed_heartbeats >= gtm->n4_persist.max_missed_heartbeats && 
    gtm->n4_persist.enabled)
{
  // 显示persist信息
}
```

## 修复后的效果

### 场景1：正常情况
```
N4 session persistence configuration:
  Status: enabled
  Persistence time: 1410065408 seconds
  Max missed heartbeats: 50000
  Current link status: normal          # 修复：362 < 50000，显示为normal
  Current missed heartbeats: 362
  # 修复：不显示persist信息，因为当前状态为normal
```

### 场景2：异常情况
```
N4 session persistence configuration:
  Status: enabled
  Persistence time: 300 seconds
  Max missed heartbeats: 5
  Current link status: anomaly         # 10 >= 5，显示为anomaly
  Current missed heartbeats: 10
  Session persist remaining time: 250.5 seconds  # 显示剩余时间
```

### 场景3：动态调整参数
当用户在运行时调整max_missed_heartbeats从3增加到50000时：
- 如果当前missed_heartbeats是10
- 原来：link_status保持为0（异常），显示不正确
- 修复后：link_status自动恢复为1（正常），显示正确

## 修改的文件

1. **upf/src/plugins/upf/upf_pfcp_server.c**
   - 修复`upf_check_n4_link_status`函数中的状态恢复逻辑

2. **upf/src/plugins/tracedump/upf/upf_pfcp_server.c**
   - 修复`upf_check_n4_link_status`函数中的状态恢复逻辑

3. **upf/src/plugins/upf/upf_cli.c**
   - 修复`upf_show_n4_persist_command_fn`函数中的显示逻辑

4. **upf/src/plugins/tracedump/upf/upf_cli.c**
   - 修复`upf_show_n4_persist_command_fn`函数中的显示逻辑
   - 删除重复的函数定义

## 测试建议

1. **基本功能测试**：
   ```bash
   set upf n4-persist enable
   set upf n4-persist time 300
   set upf n4-persist max-missed-heartbeats 5
   show upf n4-persist
   ```

2. **动态调整测试**：
   ```bash
   # 模拟心跳丢失达到阈值
   # 然后调整阈值
   set upf n4-persist max-missed-heartbeats 50000
   show upf n4-persist  # 应该显示为normal
   ```

3. **边界条件测试**：
   - 设置很大的persist_time值
   - 设置很大的max_missed_heartbeats值
   - 验证显示结果的合理性
